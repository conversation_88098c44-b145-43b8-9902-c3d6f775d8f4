import type { Metada<PERSON> } from "next";
import { GeistSans } from "geist/font/sans";
import { GeistMono } from "geist/font/mono";
import "./globals.css";
import { DashboardProvider } from '@/app/context/DashboardContext';
import Navigation from '@/components/layout/Navigation';


export const metadata: Metadata = {
  title: "LaundrySense Dashboard",
  description: "Contextually Intelligent Laundry Management",
  icons: { icon: "/favicon.ico" },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${GeistSans.variable} ${GeistMono.variable} antialiased bg-gray-50`}
      >
        <DashboardProvider>
          <div className="flex h-screen">
            {/* Sidebar Navigation */}
            <div className="w-64 flex-shrink-0 relative">
              <Navigation className="fixed top-0 left-0 h-full w-64" />
            </div>

            {/* Main Content */}
            <div className="flex-1 overflow-auto">
              <main className="p-6">
                {children}
              </main>
            </div>
          </div>
        </DashboardProvider>
      </body>
    </html>
  );
}
