import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Define enums locally
enum ServiceType {
  CUCI_KERING = 'CUCI_KERING',
  CUCI_SETRIKA = 'CUCI_SETRIKA',
  SETRIKA_SAJA = 'SETRIKA_SAJA',
  CUCI_SAJA = 'CUCI_SAJA',
  DRY_CLEAN = 'DRY_CLEAN',
  SEPATU = 'SEPATU',
  KARPET = 'KARPET',
  SELIMUT = 'SELIMUT'
}

enum TransactionStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  READY = 'READY',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

enum WeatherContext {
  SUNNY = 'SUNNY',
  RAINY = 'RAINY',
  CLOUDY = 'CLOUDY',
  STORMY = 'STORMY',
  HOT = 'HOT',
  HUMID = 'HUMID'
}

enum DayType {
  WEEKDAY = 'WEEKDAY',
  WEEKEND = 'WEEKEND',
  HOLIDAY = 'HOLIDAY',
  SPECIAL_EVENT = 'SPECIAL_EVENT'
}

enum TimeOfDay {
  EARLY_MORNING = 'EARLY_MORNING',
  MORNING = 'MORNING',
  AFTERNOON = 'AFTERNOON',
  EVENING = 'EVENING',
  NIGHT = 'NIGHT'
}

// Validation schema for transaction update
const transactionUpdateSchema = z.object({
  service_type: z.nativeEnum(ServiceType).optional(),
  weight_kg: z.number().min(0.1, 'Weight must be at least 0.1 kg').optional(),
  price: z.number().min(0, 'Price cannot be negative').optional(),
  status: z.nativeEnum(TransactionStatus).optional(),
  context_weather: z.nativeEnum(WeatherContext).optional(),
  context_day_type: z.nativeEnum(DayType).optional(),
  context_seasonal_factor: z.number().min(0).max(1).optional(),
  context_time_of_day: z.nativeEnum(TimeOfDay).optional(),
  pickup_date: z.string().datetime().optional().nullable(),
  delivery_date: z.string().datetime().optional().nullable(),
  special_instructions: z.string().max(500, 'Instructions too long').optional().nullable(),
  discount_applied: z.number().min(0).max(100).optional(),
});

// GET /api/transactions/[id] - Get transaction by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone_number: true,
            email: true,
            address: true,
          }
        },
        transaction_materials: {
          include: {
            material: {
              select: {
                id: true,
                material_name: true,
                unit_of_measure: true,
                category: true,
              }
            }
          }
        }
      }
    });

    if (!transaction) {
      return NextResponse.json(
        {
          success: false,
          error: 'Transaction not found'
        },
        { status: 404 }
      );
    }

    // Calculate total material cost
    const totalMaterialCost = transaction.transaction_materials.reduce(
      (sum: number, tm: any) => sum + tm.cost_at_time, 0
    );

    const transactionWithStats = {
      ...transaction,
      material_cost: totalMaterialCost,
      profit_margin: transaction.price - totalMaterialCost,
    };

    return NextResponse.json({
      success: true,
      data: transactionWithStats
    });

  } catch (error) {
    console.error('Error fetching transaction:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch transaction',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/transactions/[id] - Update transaction
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params; // FIXED: Await params in Next.js 15
    const body = await request.json();

    // Validate input data
    const validatedData = transactionUpdateSchema.parse(body);

    // Check if transaction exists
    const existingTransaction = await prisma.transaction.findUnique({
      where: { id }
    });

    if (!existingTransaction) {
      return NextResponse.json(
        {
          success: false,
          error: 'Transaction not found'
        },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = { ...validatedData };

    if (validatedData.pickup_date) {
      updateData.pickup_date = new Date(validatedData.pickup_date);
    }

    if (validatedData.delivery_date) {
      updateData.delivery_date = new Date(validatedData.delivery_date);
    }

    const updatedTransaction = await prisma.transaction.update({
      where: { id },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone_number: true,
          }
        },
        transaction_materials: {
          include: {
            material: {
              select: {
                id: true,
                material_name: true,
                unit_of_measure: true,
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedTransaction,
      message: 'Transaction updated successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error updating transaction:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update transaction',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/transactions/[id] - Delete transaction
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params; // FIXED: Await params in Next.js 15

    // Check if transaction exists
    const existingTransaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        transaction_materials: true
      }
    });

    if (!existingTransaction) {
      return NextResponse.json(
        {
          success: false,
          error: 'Transaction not found'
        },
        { status: 404 }
      );
    }

    // Only allow deletion if transaction is still pending
    if (existingTransaction.status !== TransactionStatus.PENDING) {
      return NextResponse.json(
        {
          success: false,
          error: 'Can only delete pending transactions'
        },
        { status: 400 }
      );
    }

    // Start transaction to ensure data consistency
    await prisma.$transaction(async (tx) => {
      // Restore material stock if materials were used
      for (const tm of existingTransaction.transaction_materials) {
        await tx.material.update({
          where: { id: tm.material_id },
          data: {
            current_stock_unit: {
              increment: tm.quantity_used
            }
          }
        });
      }

      // Delete transaction (cascade will handle transaction_materials)
      await tx.transaction.delete({
        where: { id }
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Transaction deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting transaction:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete transaction',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PATCH /api/transactions/[id]/status - Update transaction status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    const statusSchema = z.object({
      status: z.nativeEnum(TransactionStatus),
    });

    const { status } = statusSchema.parse(body);

    const existingTransaction = await prisma.transaction.findUnique({
      where: { id }
    });

    if (!existingTransaction) {
      return NextResponse.json(
        {
          success: false,
          error: 'Transaction not found'
        },
        { status: 404 }
      );
    }

    // Validate status transition
    const validTransitions: Record<TransactionStatus, TransactionStatus[]> = {
      [TransactionStatus.PENDING]: [TransactionStatus.IN_PROGRESS, TransactionStatus.CANCELLED],
      [TransactionStatus.IN_PROGRESS]: [TransactionStatus.READY, TransactionStatus.CANCELLED],
      [TransactionStatus.READY]: [TransactionStatus.COMPLETED],
      [TransactionStatus.COMPLETED]: [], // No transitions from completed
      [TransactionStatus.CANCELLED]: [], // No transitions from cancelled
    };

    if (!validTransitions[existingTransaction.status].includes(status)) {
      return NextResponse.json(
        {
          success: false,
          error: `Invalid status transition from ${existingTransaction.status} to ${status}`
        },
        { status: 400 }
      );
    }

    const updatedTransaction = await prisma.transaction.update({
      where: { id },
      data: { status },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone_number: true,
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedTransaction,
      message: `Transaction status updated to ${status}`
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error updating transaction status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update transaction status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
