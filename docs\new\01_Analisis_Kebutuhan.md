# 1. <PERSON><PERSON><PERSON> ini merinci kebutuhan fungsional dan non-fungsional untuk aplikasi "laundrysense".

## 1.1. Ke<PERSON>uhan Fungsional

Kebutuhan fungsional mendefinisikan fitur-fitur spesifik yang harus dimiliki oleh sistem untuk memenuhi tujuan bisnis.

| ID | Fitur | Deskripsi | Prioritas |
| :--- | :--- | :--- | :--- |
| **F-01** | **Manajemen Pelanggan (CRUD)** | Sistem harus memungkinkan karyawan untuk membuat, membaca, memperbarui, dan menghapus data pelanggan. Informasi yang disimpan meliputi nama, kontak, dan alamat. | Tinggi |
| **F-02** | **Manajemen Material (CRUD)** | Sistem harus menyediakan fungsionalitas untuk mengelola bahan baku laundry (deterjen, pelembut, dll.), termasuk nama material, stok saat ini, dan satuan. | Tinggi |
| **F-03** | **Manajemen Transaksi (CRUD)** | Karyawan harus dapat mencatat transaksi baru, melihat riwayat transaksi, memperbarui status (misalnya, "sedang dicuci", "siap diambil"), dan menghapus transaksi jika diperlukan. | Tinggi |
| **F-04** | **Dasbor Kontekstual Cerdas** | Sistem harus menampilkan dasbor dinamis yang menyajikan informasi dan wawasan yang relevan berdasarkan konteks saat ini (misalnya, waktu, hari, data terbaru). | Sangat Tinggi |
| **F-05** | **Analisis Pola Pelanggan** | Sistem harus dapat menganalisis data transaksi historis untuk mengidentifikasi pola perilaku pelanggan, seperti frekuensi kunjungan, layanan yang sering digunakan, dan nilai transaksi rata-rata. | Tinggi |
| **F-06** | **Manajemen Inventaris Cerdas** | Sistem harus memantau tingkat stok material dan memberikan peringatan proaktif ketika stok menipis atau mendekati batas minimum. | Sedang |
| **F-07** | **Analisis Tren Pendapatan** | Sistem harus mampu menganalisis dan memvisualisasikan tren pendapatan dari waktu ke waktu untuk membantu pengambilan keputusan strategis. | Sedang |
| **F-08** | **Sistem Peringatan** | Sistem harus menyediakan notifikasi untuk kejadian penting, seperti cucian yang lewat waktu pengambilan (`overdue pickups`) dan stok material yang rendah. | Tinggi |

## 1.2. Kebutuhan Non-Fungsional

Kebutuhan non-fungsional mendefinisikan kualitas dan batasan dari sistem.

| ID | Kategori | Kebutuhan | Deskripsi |
| :--- | :--- | :--- | :--- |
| **NF-01** | **Kinerja** | **Pembaruan Real-time** | Dasbor dan metrik utama harus diperbarui secara real-time (atau mendekati real-time) saat transaksi baru masuk. Ini dicapai melalui WebSocket. |
| **NF-02** | **Skalabilitas** | **Penanganan Beban** | Arsitektur harus mampu menangani peningkatan jumlah transaksi dan data seiring pertumbuhan bisnis tanpa degradasi kinerja yang signifikan. |
| **NF-03** | **Keamanan** | **Akses Data** | Akses ke data sensitif pelanggan dan bisnis harus dibatasi. Interaksi dengan database harus dilakukan melalui ORM (Prisma) untuk mencegah injeksi SQL. |
| **NF-04** | **Keandalan** | **Ketersediaan** | Sistem harus memiliki ketersediaan tinggi selama jam operasional bisnis. |
| **NF-05** | **Keandalan** | **Integritas Data** | Validasi data (menggunakan Zod) harus diterapkan di sisi server untuk memastikan data yang masuk ke database konsisten dan akurat. |
| **NF-06** | **Pemeliharaan** | **Keterbacaan Kode** | Kode harus ditulis dengan baik, terstruktur, dan dikomentari jika perlu untuk memudahkan pemeliharaan dan pengembangan di masa depan. |
| **NF-07** | **Pemeliharaan** | **Automasi Tugas** | Proses analisis data yang berat harus dijalankan secara otomatis di latar belakang (menggunakan `node-cron`) untuk tidak mengganggu kinerja aplikasi utama. |