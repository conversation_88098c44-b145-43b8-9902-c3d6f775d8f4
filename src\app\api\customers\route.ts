import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for customer creation/update
const customerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  phone_number: z.string().regex(/^[0-9+\-\s()]+$/, 'Invalid phone number format'),
  email: z.string().email('Invalid email format').optional().nullable(),
  address: z.string().max(500, 'Address too long').optional().nullable(),
});

// GET /api/customers - Get all customers or by query
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'registration_date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause for search (MySQL compatible)
    const where = search ? {
      OR: [
        { name: { contains: search } },
        { phone_number: { contains: search } },
        { email: { contains: search } },
      ]
    } : {};

    // Build orderBy clause
    const orderBy = { [sortBy]: sortOrder };

    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          _count: {
            select: { transactions: true }
          },
          customer_pattern: true
        }
      }),
      prisma.customer.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        customers,
        total,
      },
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch customers',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/customers - Create new customer
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input data
    const validatedData = customerSchema.parse(body);

    // Check if phone number already exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { phone_number: validatedData.phone_number }
    });

    if (existingCustomer) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer with this phone number already exists'
        },
        { status: 400 }
      );
    }

    // Check if email already exists (if provided)
    if (validatedData.email) {
      const existingEmail = await prisma.customer.findUnique({
        where: { email: validatedData.email }
      });

      if (existingEmail) {
        return NextResponse.json(
          {
            success: false,
            error: 'Customer with this email already exists'
          },
          { status: 400 }
        );
      }
    }

    const customer = await prisma.customer.create({
      data: validatedData,
      include: {
        _count: {
          select: { transactions: true }
        }
      }
    });

    return NextResponse.json(
      {
        success: true,
        data: customer,
        message: 'Customer created successfully'
      },
      { status: 201 }
    );

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error creating customer:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create customer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
