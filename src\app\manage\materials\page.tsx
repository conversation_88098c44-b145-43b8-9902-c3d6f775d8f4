"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useMaterials } from '@/hooks/useApi';
import DataTable from '@/components/ui/DataTable';
import Modal from '@/components/ui/Modal';
import FormField from '@/components/ui/FormField';
import { Package, AlertTriangle, DollarSign, Calendar } from 'lucide-react';

interface Material {
  id: string;
  material_name: string;
  current_stock_unit: number;
  unit_of_measure: string;
  last_restock_date: string;
  usage_rate_per_transaction: number;
  usage_rate_per_kg: number;
  minimum_stock_threshold: number;
  cost_per_unit: number;
  supplier_info?: string;
  category: string;
}

interface MaterialFormData {
  material_name: string;
  current_stock_unit: number;
  unit_of_measure: string;
  usage_rate_per_transaction: number;
  usage_rate_per_kg: number;
  minimum_stock_threshold: number;
  cost_per_unit: number;
  supplier_info: string;
  category: string;
}

interface MaterialFormErrors {
  material_name?: string;
  current_stock_unit?: string;
  unit_of_measure?: string;
  usage_rate_per_transaction?: string;
  usage_rate_per_kg?: string;
  minimum_stock_threshold?: string;
  cost_per_unit?: string;
  supplier_info?: string;
  category?: string;
}

const MATERIAL_CATEGORIES = [
  { value: 'DETERGENT', label: 'Deterjen' },
  { value: 'FABRIC_SOFTENER', label: 'Pelembut Kain' },
  { value: 'BLEACH', label: 'Pemutih' },
  { value: 'STAIN_REMOVER', label: 'Penghilang Noda' },
  { value: 'PACKAGING', label: 'Kemasan' },
  { value: 'EQUIPMENT', label: 'Peralatan' },
  { value: 'FRAGRANCE', label: 'Pewangi' },
  { value: 'OTHER', label: 'Lainnya' },
];

const UNITS_OF_MEASURE = [
  { value: 'ml', label: 'Mililiter (ml)' },
  { value: 'liter', label: 'Liter' },
  { value: 'gram', label: 'Gram' },
  { value: 'kg', label: 'Kilogram' },
  { value: 'piece', label: 'Buah' },
];

const MaterialsPage: React.FC = () => {
  const [materials, setMaterials] = useState<Material[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null);
  const [formData, setFormData] = useState<MaterialFormData>({
    material_name: '',
    current_stock_unit: 0,
    unit_of_measure: '',
    usage_rate_per_transaction: 0,
    usage_rate_per_kg: 0,
    minimum_stock_threshold: 0,
    cost_per_unit: 0,
    supplier_info: '',
    category: '',
  });
  const [formErrors, setFormErrors] = useState<MaterialFormErrors>({});

  const {
    loading,
    error,
    getMaterials,
    createMaterial,
    updateMaterial,
    deleteMaterial,
  } = useMaterials();

  const [isLoading, setIsLoading] = useState(false);

  const loadMaterials = useCallback(async () => {
    if (isLoading) return; // Prevent multiple simultaneous calls

    setIsLoading(true);
    try {
      const response = await getMaterials();
      if (response.success && response.data) {
        setMaterials(response.data);
      }
    } finally {
      setIsLoading(false);
    }
  }, [getMaterials, isLoading]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadMaterials();
    }, 100); // Debounce to prevent rapid calls

    return () => clearTimeout(timeoutId);
  }, []); // Empty dependency array - only run once on mount

  const validateForm = (): boolean => {
    const errors: MaterialFormErrors = {};

    if (!formData.material_name.trim()) {
      errors.material_name = 'Nama material wajib diisi';
    }

    if (!formData.unit_of_measure) {
      errors.unit_of_measure = 'Satuan wajib dipilih';
    }

    if (!formData.category) {
      errors.category = 'Kategori wajib dipilih';
    }

    if (formData.current_stock_unit < 0) {
      errors.current_stock_unit = 'Stok tidak boleh negatif';
    }

    if (formData.cost_per_unit < 0) {
      errors.cost_per_unit = 'Harga tidak boleh negatif';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    const submitData = {
      ...formData,
      supplier_info: formData.supplier_info || null,
    };

    let response;
    if (editingMaterial) {
      response = await updateMaterial(editingMaterial.id, submitData);
    } else {
      response = await createMaterial(submitData);
    }

    if (response.success) {
      setIsModalOpen(false);
      resetForm();
      loadMaterials();
    }
  };

  const handleEdit = (material: Material) => {
    setEditingMaterial(material);
    setFormData({
      material_name: material.material_name,
      current_stock_unit: material.current_stock_unit,
      unit_of_measure: material.unit_of_measure,
      usage_rate_per_transaction: material.usage_rate_per_transaction,
      usage_rate_per_kg: material.usage_rate_per_kg,
      minimum_stock_threshold: material.minimum_stock_threshold,
      cost_per_unit: material.cost_per_unit,
      supplier_info: material.supplier_info || '',
      category: material.category,
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (material: Material) => {
    if (window.confirm(`Apakah Anda yakin ingin menghapus material ${material.material_name}?`)) {
      const response = await deleteMaterial(material.id);
      if (response.success) {
        loadMaterials();
      }
    }
  };

  const resetForm = () => {
    setFormData({
      material_name: '',
      current_stock_unit: 0,
      unit_of_measure: '',
      usage_rate_per_transaction: 0,
      usage_rate_per_kg: 0,
      minimum_stock_threshold: 0,
      cost_per_unit: 0,
      supplier_info: '',
      category: '',
    });
    setFormErrors({});
    setEditingMaterial(null);
  };

  const handleCreate = () => {
    resetForm();
    setIsModalOpen(true);
  };

  const getCategoryLabel = (category: string) => {
    const cat = MATERIAL_CATEGORIES.find(c => c.value === category);
    return cat ? cat.label : category;
  };

  const getStockStatus = (current: number, threshold: number) => {
    if (current <= threshold) {
      return { status: 'low', color: 'text-red-600 bg-red-100', label: 'Stok Rendah' };
    } else if (current <= threshold * 1.5) {
      return { status: 'medium', color: 'text-yellow-600 bg-yellow-100', label: 'Perlu Perhatian' };
    } else {
      return { status: 'good', color: 'text-green-600 bg-green-100', label: 'Stok Aman' };
    }
  };

  const columns = [
    {
      key: 'material_name',
      label: 'Nama Material',
      sortable: true,
      render: (value: string, row: Material) => (
        <div className="flex items-center gap-2">
          <Package size={16} className="text-gray-400" />
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-gray-500">{getCategoryLabel(row.category)}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'current_stock_unit',
      label: 'Stok',
      render: (value: number, row: Material) => {
        const stockStatus = getStockStatus(value, row.minimum_stock_threshold);
        return (
          <div className="space-y-1">
            <div className="font-medium">{value} {row.unit_of_measure}</div>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>
              {stockStatus.label}
            </span>
          </div>
        );
      },
    },
    {
      key: 'minimum_stock_threshold',
      label: 'Batas Minimum',
      render: (value: number, row: Material) => (
        <div className="flex items-center gap-1">
          <AlertTriangle size={14} className="text-yellow-500" />
          <span>{value} {row.unit_of_measure}</span>
        </div>
      ),
    },
    {
      key: 'cost_per_unit',
      label: 'Harga/Unit',
      render: (value: number) => (
        <div className="flex items-center gap-1">
          <DollarSign size={14} className="text-green-500" />
          <span>Rp {value.toLocaleString('id-ID')}</span>
        </div>
      ),
    },
    {
      key: 'usage_rate_per_transaction',
      label: 'Penggunaan/Transaksi',
      render: (value: number, row: Material) => (
        <span className="text-sm">{value} {row.unit_of_measure}</span>
      ),
    },
    {
      key: 'last_restock_date',
      label: 'Restock Terakhir',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Calendar size={16} className="text-gray-400" />
          <span className="text-sm">{new Date(value).toLocaleDateString('id-ID')}</span>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Manajemen Material</h1>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <DataTable
        title="Daftar Material"
        columns={columns}
        data={materials}
        loading={loading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onCreate={handleCreate}
        emptyMessage="Belum ada material terdaftar"
      />

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingMaterial ? 'Edit Material' : 'Tambah Material'}
        size="lg"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Nama Material"
              name="material_name"
              value={formData.material_name}
              onChange={(value) => setFormData({ ...formData, material_name: value as string })}
              error={formErrors.material_name}
              required
            />

            <FormField
              label="Kategori"
              name="category"
              type="select"
              value={formData.category}
              onChange={(value) => setFormData({ ...formData, category: value as string })}
              error={formErrors.category}
              options={MATERIAL_CATEGORIES}
              required
            />

            <FormField
              label="Stok Saat Ini"
              name="current_stock_unit"
              type="number"
              value={formData.current_stock_unit}
              onChange={(value) => setFormData({ ...formData, current_stock_unit: value as number })}
              error={formErrors.current_stock_unit}
              min={0}
              step={0.1}
              required
            />

            <FormField
              label="Satuan"
              name="unit_of_measure"
              type="select"
              value={formData.unit_of_measure}
              onChange={(value) => setFormData({ ...formData, unit_of_measure: value as string })}
              error={formErrors.unit_of_measure}
              options={UNITS_OF_MEASURE}
              required
            />

            <FormField
              label="Batas Minimum Stok"
              name="minimum_stock_threshold"
              type="number"
              value={formData.minimum_stock_threshold}
              onChange={(value) => setFormData({ ...formData, minimum_stock_threshold: value as number })}
              min={0}
              step={0.1}
            />

            <FormField
              label="Harga per Unit"
              name="cost_per_unit"
              type="number"
              value={formData.cost_per_unit}
              onChange={(value) => setFormData({ ...formData, cost_per_unit: value as number })}
              error={formErrors.cost_per_unit}
              min={0}
              step={0.01}
            />

            <FormField
              label="Penggunaan per Transaksi"
              name="usage_rate_per_transaction"
              type="number"
              value={formData.usage_rate_per_transaction}
              onChange={(value) => setFormData({ ...formData, usage_rate_per_transaction: value as number })}
              min={0}
              step={0.1}
            />

            <FormField
              label="Penggunaan per Kg"
              name="usage_rate_per_kg"
              type="number"
              value={formData.usage_rate_per_kg}
              onChange={(value) => setFormData({ ...formData, usage_rate_per_kg: value as number })}
              min={0}
              step={0.1}
            />
          </div>

          <FormField
            label="Info Supplier"
            name="supplier_info"
            type="textarea"
            value={formData.supplier_info}
            onChange={(value) => setFormData({ ...formData, supplier_info: value as string })}
            rows={2}
          />

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Menyimpan...' : editingMaterial ? 'Update' : 'Simpan'}
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default MaterialsPage;
