"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Search, User, Plus, Calculator, Calendar, Clock, MapPin, AlertCircle, CheckCircle2 } from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  address?: string;
}

interface Material {
  id: string;
  material_name: string;
  cost_per_unit: number;
  unit_of_measure: string;
}

interface TransactionFormData {
  customer_id: string;
  service_type: string;
  weight_kg: number;
  price: number;
  status: string;
  context_weather: string;
  context_day_type: string;
  context_time_of_day: string;
  pickup_date: string;
  delivery_date: string;
  special_instructions: string;
  discount_applied: number;
  materials: Array<{
    material_id: string;
    quantity_used: number;
  }>;
}

interface EnhancedTransactionFormProps {
  initialData?: Partial<TransactionFormData>;
  customers: Customer[];
  materials: Material[];
  onSubmit: (data: TransactionFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  isEditing?: boolean;
}

const SERVICE_TYPES = [
  { value: 'CUCI_KERING', label: 'Cuci & Kering', basePrice: 8000, estimatedHours: 24 },
  { value: 'CUCI_SETRIKA', label: 'Cuci & Setrika', basePrice: 12000, estimatedHours: 48 },
  { value: 'SETRIKA_SAJA', label: 'Setrika Saja', basePrice: 5000, estimatedHours: 12 },
  { value: 'CUCI_SAJA', label: 'Cuci Saja', basePrice: 6000, estimatedHours: 12 },
  { value: 'DRY_CLEAN', label: 'Dry Clean', basePrice: 25000, estimatedHours: 72 },
  { value: 'SEPATU', label: 'Cuci Sepatu', basePrice: 15000, estimatedHours: 24 },
  { value: 'KARPET', label: 'Cuci Karpet', basePrice: 20000, estimatedHours: 48 },
  { value: 'SELIMUT', label: 'Cuci Selimut', basePrice: 18000, estimatedHours: 36 },
];

const WEATHER_OPTIONS = [
  { value: 'SUNNY', label: '☀️ Cerah', multiplier: 1.0 },
  { value: 'CLOUDY', label: '☁️ Berawan', multiplier: 1.1 },
  { value: 'RAINY', label: '🌧️ Hujan', multiplier: 1.2 },
];

const EnhancedTransactionForm: React.FC<EnhancedTransactionFormProps> = ({
  initialData,
  customers,
  materials,
  onSubmit,
  onCancel,
  isLoading = false,
  isEditing = false,
}) => {
  const [formData, setFormData] = useState<TransactionFormData>({
    customer_id: '',
    service_type: '',
    weight_kg: 0,
    price: 0,
    status: 'PENDING',
    context_weather: 'SUNNY',
    context_day_type: 'WEEKDAY',
    context_time_of_day: 'MORNING',
    pickup_date: '',
    delivery_date: '',
    special_instructions: '',
    discount_applied: 0,
    materials: [],
    ...initialData,
  });

  const [customerSearch, setCustomerSearch] = useState('');
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Smart customer search with autocomplete
  const filteredCustomers = useMemo(() => {
    if (!customerSearch) return customers.slice(0, 5); // Show recent customers
    
    return customers.filter(customer =>
      customer.name.toLowerCase().includes(customerSearch.toLowerCase()) ||
      customer.phone_number.includes(customerSearch)
    ).slice(0, 10);
  }, [customers, customerSearch]);

  // Auto-calculate price based on service type, weight, and weather
  const calculatedPrice = useMemo(() => {
    const serviceType = SERVICE_TYPES.find(s => s.value === formData.service_type);
    if (!serviceType || !formData.weight_kg) return 0;

    const weatherMultiplier = WEATHER_OPTIONS.find(w => w.value === formData.context_weather)?.multiplier || 1.0;
    const basePrice = serviceType.basePrice * formData.weight_kg * weatherMultiplier;
    const discountAmount = (basePrice * formData.discount_applied) / 100;
    
    return Math.round(basePrice - discountAmount);
  }, [formData.service_type, formData.weight_kg, formData.context_weather, formData.discount_applied]);

  // Auto-calculate delivery date based on pickup date and service type
  useEffect(() => {
    if (formData.pickup_date && formData.service_type) {
      const serviceType = SERVICE_TYPES.find(s => s.value === formData.service_type);
      if (serviceType) {
        const pickupDate = new Date(formData.pickup_date);
        const deliveryDate = new Date(pickupDate);
        deliveryDate.setHours(deliveryDate.getHours() + serviceType.estimatedHours);
        
        setFormData(prev => ({
          ...prev,
          delivery_date: deliveryDate.toISOString().split('T')[0]
        }));
      }
    }
  }, [formData.pickup_date, formData.service_type]);

  // Update price when calculated price changes
  useEffect(() => {
    if (calculatedPrice > 0) {
      setFormData(prev => ({ ...prev, price: calculatedPrice }));
    }
  }, [calculatedPrice]);

  // Auto-detect day type and time of day
  useEffect(() => {
    if (formData.pickup_date) {
      const date = new Date(formData.pickup_date);
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      
      setFormData(prev => ({
        ...prev,
        context_day_type: isWeekend ? 'WEEKEND' : 'WEEKDAY'
      }));
    }
  }, [formData.pickup_date]);

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    setFormData(prev => ({ ...prev, customer_id: customer.id }));
    setCustomerSearch(customer.name);
    setShowCustomerDropdown(false);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customer_id) newErrors.customer_id = 'Pelanggan wajib dipilih';
    if (!formData.service_type) newErrors.service_type = 'Jenis layanan wajib dipilih';
    if (formData.weight_kg <= 0) newErrors.weight_kg = 'Berat harus lebih dari 0 kg';
    if (!formData.pickup_date) newErrors.pickup_date = 'Tanggal pickup wajib diisi';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    await onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-responsive max-w-6xl mx-auto p-responsive">
      {/* Customer Selection Section */}
      <div className="card">
        <div className="card-header">
          <User className="w-5 h-5 text-blue-600" />
          <h3 className="text-responsive-lg font-semibold text-gray-900">Pilih Pelanggan</h3>
        </div>
        
        <div className="relative">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Cari nama atau nomor telepon pelanggan..."
              value={customerSearch}
              onChange={(e) => {
                setCustomerSearch(e.target.value);
                setShowCustomerDropdown(true);
              }}
              onFocus={() => setShowCustomerDropdown(true)}
              className="form-input pl-10"
            />
          </div>
          
          {showCustomerDropdown && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {filteredCustomers.length > 0 ? (
                <>
                  {filteredCustomers.map((customer) => (
                    <button
                      key={customer.id}
                      type="button"
                      onClick={() => handleCustomerSelect(customer)}
                      className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors"
                    >
                      <div className="font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.phone_number}</div>
                    </button>
                  ))}
                  <button
                    type="button"
                    className="w-full px-4 py-3 text-left text-blue-600 hover:bg-blue-50 border-t border-gray-200 flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Tambah Pelanggan Baru
                  </button>
                </>
              ) : (
                <div className="px-4 py-3 text-gray-500 text-center">
                  Pelanggan tidak ditemukan
                </div>
              )}
            </div>
          )}
        </div>
        
        {selectedCustomer && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle2 className="w-4 h-4" />
              <span className="font-medium">Pelanggan Terpilih:</span>
            </div>
            <div className="mt-1 text-green-700">
              <div className="font-medium">{selectedCustomer.name}</div>
              <div className="text-sm">{selectedCustomer.phone_number}</div>
              {selectedCustomer.address && (
                <div className="text-sm flex items-center gap-1 mt-1">
                  <MapPin className="w-3 h-3" />
                  {selectedCustomer.address}
                </div>
              )}
            </div>
          </div>
        )}
        
        {errors.customer_id && (
          <div className="mt-2 flex items-center gap-1 text-red-600 text-sm">
            <AlertCircle className="w-4 h-4" />
            {errors.customer_id}
          </div>
        )}
      </div>

      {/* Service & Pricing Section */}
      <div className="card">
        <div className="flex items-center gap-2 mb-4">
          <Calculator className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">Layanan & Harga</h3>
        </div>
        
        <div className="mobile-grid mobile-grid-md gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Jenis Layanan <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.service_type}
              onChange={(e) => setFormData(prev => ({ ...prev, service_type: e.target.value }))}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="">Pilih jenis layanan</option>
              {SERVICE_TYPES.map((service) => (
                <option key={service.value} value={service.value}>
                  {service.label} - Rp {service.basePrice.toLocaleString('id-ID')}/kg
                </option>
              ))}
            </select>
            {errors.service_type && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.service_type}
              </div>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Berat (kg) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              step="0.1"
              min="0.1"
              value={formData.weight_kg}
              onChange={(e) => setFormData(prev => ({ ...prev, weight_kg: parseFloat(e.target.value) || 0 }))}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="0.0"
            />
            {errors.weight_kg && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.weight_kg}
              </div>
            )}
          </div>
        </div>
        
        <div className="mobile-grid mobile-grid-lg gap-4 mt-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Cuaca</label>
            <select
              value={formData.context_weather}
              onChange={(e) => setFormData(prev => ({ ...prev, context_weather: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              {WEATHER_OPTIONS.map((weather) => (
                <option key={weather.value} value={weather.value}>
                  {weather.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Diskon (%)</label>
            <input
              type="number"
              min="0"
              max="100"
              value={formData.discount_applied}
              onChange={(e) => setFormData(prev => ({ ...prev, discount_applied: parseFloat(e.target.value) || 0 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="0"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Total Harga</label>
            <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-lg font-semibold text-green-600">
              Rp {calculatedPrice.toLocaleString('id-ID')}
            </div>
          </div>
        </div>
      </div>

      {/* Scheduling Section */}
      <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="w-5 h-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">Jadwal Pickup & Delivery</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tanggal Pickup <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              value={formData.pickup_date}
              onChange={(e) => setFormData(prev => ({ ...prev, pickup_date: e.target.value }))}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
            {errors.pickup_date && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.pickup_date}
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estimasi Delivery
            </label>
            <input
              type="date"
              value={formData.delivery_date}
              onChange={(e) => setFormData(prev => ({ ...prev, delivery_date: e.target.value }))}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              readOnly
            />
            <div className="mt-1 text-xs text-gray-500 flex items-center gap-1">
              <Clock className="w-3 h-3" />
              Otomatis dihitung berdasarkan jenis layanan
            </div>
          </div>
        </div>

        {formData.service_type && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm text-blue-800">
              <div className="font-medium">Estimasi Pengerjaan:</div>
              <div className="mt-1">
                {SERVICE_TYPES.find(s => s.value === formData.service_type)?.estimatedHours} jam
                ({formData.context_day_type === 'WEEKEND' ? 'Akhir pekan' : 'Hari kerja'})
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Additional Information */}
      <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Informasi Tambahan</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Instruksi Khusus
            </label>
            <textarea
              value={formData.special_instructions}
              onChange={(e) => setFormData(prev => ({ ...prev, special_instructions: e.target.value }))}
              rows={3}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
              placeholder="Catatan khusus untuk pesanan ini..."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="PENDING">🟡 Pending</option>
                <option value="IN_PROGRESS">🔵 Sedang Dikerjakan</option>
                <option value="READY">🟢 Siap Diambil</option>
                <option value="COMPLETED">✅ Selesai</option>
                <option value="CANCELLED">❌ Dibatalkan</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Waktu Pickup</label>
              <select
                value={formData.context_time_of_day}
                onChange={(e) => setFormData(prev => ({ ...prev, context_time_of_day: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="MORNING">🌅 Pagi (06:00-12:00)</option>
                <option value="AFTERNOON">☀️ Siang (12:00-18:00)</option>
                <option value="EVENING">🌆 Sore (18:00-21:00)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Price Breakdown */}
      {formData.service_type && formData.weight_kg > 0 && (
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200 p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Rincian Harga</h3>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Harga Dasar ({formData.weight_kg} kg)</span>
              <span className="font-medium">
                Rp {(SERVICE_TYPES.find(s => s.value === formData.service_type)?.basePrice || 0 * formData.weight_kg).toLocaleString('id-ID')}
              </span>
            </div>

            {formData.context_weather !== 'SUNNY' && (
              <div className="flex justify-between items-center text-orange-600">
                <span>Penyesuaian Cuaca ({WEATHER_OPTIONS.find(w => w.value === formData.context_weather)?.label})</span>
                <span>+{((WEATHER_OPTIONS.find(w => w.value === formData.context_weather)?.multiplier || 1) - 1) * 100}%</span>
              </div>
            )}

            {formData.discount_applied > 0 && (
              <div className="flex justify-between items-center text-red-600">
                <span>Diskon ({formData.discount_applied}%)</span>
                <span>-Rp {Math.round((calculatedPrice / (1 - formData.discount_applied / 100)) * (formData.discount_applied / 100)).toLocaleString('id-ID')}</span>
              </div>
            )}

            <div className="border-t border-gray-300 pt-3">
              <div className="flex justify-between items-center text-lg font-bold text-green-600">
                <span>Total</span>
                <span>Rp {calculatedPrice.toLocaleString('id-ID')}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="mobile-stack mobile-stack-sm pt-6">
        <button
          type="button"
          onClick={onCancel}
          className="flex-1 btn-secondary"
        >
          Batal
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="flex-1 btn-primary flex items-center justify-center gap-2"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Menyimpan...
            </>
          ) : (
            <>
              <CheckCircle2 className="w-4 h-4" />
              {isEditing ? 'Update Transaksi' : 'Simpan Transaksi'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default EnhancedTransactionForm;
