# 4. Arsitektur Aplikasi

Dokumen ini menguraikan arsitektur data-driven dari aplikasi "laundrysense", yang dirancang untuk mengubah data mentah menjadi nilai bisnis yang dapat ditindaklanjuti. Arsitektur ini terdiri dari beberapa lapisan yang bekerja sama untuk mengumpulkan, memp<PERSON><PERSON>, mengan<PERSON><PERSON>, dan menyajikan informasi.

## 4.1. Gambaran Umum Arsitektur

Arsitektur `laundrysense` dapat divisualisasikan sebagai sebuah siklus data yang berkelanjutan. Data tidak hanya disimpan dan diambil, tetapi juga dianalisis secara terus-menerus untuk memperkaya pengalaman pengguna dan memberikan wawasan operasional.

```
                 +--------------------------------+
                 |       Frontend (Next.js)       |
                 | - Interaksi Pen<PERSON>una (CRUD)    |
                 | - <PERSON><PERSON><PERSON> Dasbor Kontekstual  |
                 | - Koneksi WebSocket            |
                 +-----------------|--------------+
                                   | (1) Interaksi & (5) Tampilan
                                   v
+--------------------------------+--------------------------------+
|        Backend (Next.js API Routes)        |      WebSocket Server      |
| - Endpoint CRUD (/api/customers, etc.)     | - Notifikasi Real-time     |
| - Endpoint Analitik (/api/analytics)       |                            |
+-----------------|--------------------------+--------------------------+
                  | (2) Penulisan & Pembacaan Data
                  v
+-----------------+--------------------------+
|          Database (MySQL & Prisma)         |
| - Data Transaksional (Customer, Transaksi) |
| - Data Analitik (CustomerPattern, dll.)    |
+-----------------|--------------------------+
                  | (3) Pembacaan Data Historis
                  v
+-----------------+--------------------------+
|        Cron Job (node-cron)                |
| - Menjalankan PatternAnalysisService       |
| - Menghitung & Menyimpan Pola (harian)     |
+-----------------|--------------------------+
                  | (4) Penggunaan Pola & Data Real-time
                  v
+-----------------+--------------------------+
|        Contextual Engine                   |
| - Menganalisis Konteks Saat Ini            |
| - Menghasilkan Wawasan Cerdas              |
+--------------------------------------------+
```

## 4.2. Alur Data

Alur data dalam aplikasi ini dapat dipecah menjadi beberapa langkah utama:

**1. Interaksi Pengguna (Frontend):**
*   Pengguna (karyawan) melakukan operasi standar seperti menambahkan pelanggan baru atau mencatat transaksi melalui antarmuka pengguna yang dibangun dengan React dan komponen UI lainnya.
*   Setiap tindakan ini memicu panggilan ke endpoint API di backend.
*   Dasbor (`/dashboard`) membuka koneksi WebSocket untuk menerima pembaruan real-time.

**2. Pemrosesan Backend (API Routes):**
*   API Routes yang dibangun di Next.js menerima permintaan dari frontend.
*   Data divalidasi menggunakan Zod untuk memastikan integritasnya.
*   Prisma ORM digunakan untuk berinteraksi dengan database MySQL, melakukan operasi `create`, `read`, `update`, atau `delete` pada tabel yang relevan (misalnya, `Transaction`, `Customer`).
*   Setelah transaksi baru berhasil dibuat, backend mengirimkan notifikasi melalui WebSocket Server untuk memberi tahu semua klien yang terhubung bahwa ada data baru.

**3. Analisis Latar Belakang (Cron Job & `PatternAnalysisService`):**
*   Setiap malam (atau sesuai jadwal), sebuah cron job yang dikelola oleh `node-cron` berjalan secara otomatis.
*   Job ini memicu `PatternAnalysisService` (`src/lib/pattern-analysis`).
*   Layanan ini membaca data transaksional historis dari database (misalnya, semua transaksi dalam 3 bulan terakhir).
*   Ia melakukan perhitungan untuk mengidentifikasi pola, seperti frekuensi kunjungan rata-rata pelanggan, material yang paling sering digunakan, dan tren pendapatan.
*   Hasil analisis ini disimpan dalam tabel analitik khusus (misalnya, `CustomerPattern`, `MaterialUsagePattern`, `RevenueTrend`). Langkah ini penting untuk memisahkan beban kerja analitik dari operasi real-time.

**4. Generasi Wawasan (Contextual Engine):**
*   Ketika pengguna mengunjungi dasbor, `DashboardContext` di frontend memicu `ContextualEngine` di backend.
*   `ContextualEngine` (`src/lib/contextual-intelligence`) membaca data dari berbagai sumber:
    *   **Data Real-time:** Transaksi terbaru, status stok saat ini.
    *   **Data Analitik:** Pola pelanggan dan tren pendapatan yang telah dihitung oleh cron job.
    *   **Konteks Eksternal:** Waktu dan tanggal saat ini.
*   Menggunakan `ContextDetector` dan `SmartInsightGenerator`, engine ini mensintesis semua data ini untuk menghasilkan wawasan yang relevan.

**5. Penyajian Nilai (Frontend):**
*   Wawasan yang dihasilkan oleh `ContextualEngine` dikirim kembali ke frontend.
*   Komponen seperti `ContextualHeader` dan `ProgressiveInsightCard` menampilkan wawasan ini kepada pengguna dengan cara yang mudah dipahami dan dapat ditindaklanjuti.
*   Siklus ini berulang saat data baru masuk, memastikan dasbor selalu hidup dan relevan.

## 4.3. Kesimpulan

Arsitektur ini secara efektif memisahkan berbagai "concerns": antarmuka pengguna, logika bisnis, interaksi database, dan analisis data berat. Dengan menggunakan cron job untuk pra-pemrosesan analitik dan WebSocket untuk pembaruan real-time, aplikasi dapat tetap responsif sambil memberikan wawasan yang mendalam dan didukung oleh data historis.