# 5. Entity Relationship Diagram (ERD)

Dokumen ini menyediakan representasi Entity Relationship Diagram (ERD) untuk database aplikasi `laundrysense`, berdasarkan skema Prisma. ERD ini menggambarkan entitas data utama, atributnya, dan hubungan di antara mereka.

## 5.1. Diagram ERD (Mermaid Syntax)

```mermaid
erDiagram
    CUSTOMER ||--o{ TRANSACTION : "places"
    CUSTOMER ||--|| CUSTOMER_PATTERN : "has one"
    TRANSACTION }o--o|{ TRANSACTION_MATERIAL : "contains"
    MATERIAL ||--o|{ TRANSACTION_MATERIAL : "is used in"
    MATERIAL ||--|| MATERIAL_USAGE_PATTERN : "has one"

    CUSTOMER {
        Int id PK "Primary Key"
        String name "Nama Pelanggan"
        String phone "Nomor Telepon"
        String address "Alamat"
        DateTime createdAt "Waktu Dibuat"
    }

    TRANSACTION {
        Int id PK "Primary Key"
        Int customerId FK "Foreign Key to CUSTOMER"
        String status "Status Cucian (e.g., 'processed', 'completed')"
        Float totalAmount "Total Biaya"
        DateTime createdAt "Waktu Transaksi"
        DateTime pickupDate "Jadwal Pengambilan"
    }

    TRANSACTION_MATERIAL {
        Int transactionId PK, FK "Composite PK, FK to TRANSACTION"
        Int materialId PK, FK "Composite PK, FK to MATERIAL"
        Int quantity "Jumlah Material Digunakan"
    }

    MATERIAL {
        Int id PK "Primary Key"
        String name "Nama Material (e.g., Deterjen)"
        Int stock "Jumlah Stok"
        String unit "Satuan (e.g., 'kg', 'liter')"
    }

    CUSTOMER_PATTERN {
        Int id PK "Primary Key"
        Int customerId FK "Foreign Key to CUSTOMER"
        Float avgVisitFrequencyDays "Rata-rata Frekuensi Kunjungan (hari)"
        Float avgTransactionValue "Rata-rata Nilai Transaksi"
        DateTime lastVisitDate "Tanggal Kunjungan Terakhir"
    }

    MATERIAL_USAGE_PATTERN {
        Int id PK "Primary Key"
        Int materialId FK "Foreign Key to MATERIAL"
        Float avgWeeklyConsumption "Rata-rata Konsumsi Mingguan"
        DateTime lastUpdated "Waktu Terakhir Diperbarui"
    }

    REVENUE_TREND {
        Int id PK "Primary Key"
        Date date "Tanggal"
        Float dailyRevenue "Pendapatan Harian"
        Float weeklyMovingAvg "Rata-rata Pendapatan Mingguan Bergerak"
    }

    PATTERN_CALCULATION_LOG {
        Int id PK "Primary Key"
        DateTime startTime "Waktu Mulai Kalkulasi"
        DateTime endTime "Waktu Selesai Kalkulasi"
        String status "Status (e.g., 'success', 'failed')"
        String notes "Catatan atau Error"
    }
```

## 5.2. Deskripsi Entitas

### Entitas Inti (Transaksional)
*   **`Customer`**: Menyimpan data demografis pelanggan. Ini adalah entitas pusat untuk memahami siapa pelanggan kita.
*   **`Transaction`**: Mencatat setiap transaksi laundry yang dilakukan oleh pelanggan. Ini adalah sumber data utama untuk semua analisis.
*   **`Material`**: Menyimpan informasi tentang bahan baku yang digunakan dalam operasi laundry, termasuk tingkat stok saat ini.
*   **`TransactionMaterial`**: Tabel penghubung (join table) yang mengimplementasikan hubungan *many-to-many* antara `Transaction` dan `Material`. Tabel ini mencatat material apa saja dan berapa banyak yang digunakan dalam setiap transaksi.

### Entitas Analitik (Hasil Proses)
*   **`CustomerPattern`**: Menyimpan hasil analisis dari `PatternAnalysisService`. Entitas ini merangkum perilaku kunci seorang pelanggan (misalnya, seberapa sering dia datang, berapa rata-rata belanjanya). Ini adalah hasil olahan, bukan data mentah.
*   **`MaterialUsagePattern`**: Mirip dengan `CustomerPattern`, tetapi untuk material. Ini menyimpan data tentang seberapa cepat suatu material dikonsumsi, yang penting untuk manajemen inventaris proaktif.
*   **`RevenueTrend`**: Menyimpan data tren pendapatan harian dan mingguan. Digunakan untuk visualisasi dan analisis kinerja bisnis jangka panjang.
*   **`PatternCalculationLog`**: Tabel logging yang mencatat setiap eksekusi dari `PatternAnalysisService`. Ini penting untuk pemantauan dan debugging proses analisis latar belakang.

## 5.3. Relasi Utama

*   **`Customer` 1-to-Many `Transaction`**: Satu pelanggan dapat memiliki banyak transaksi, tetapi setiap transaksi hanya milik satu pelanggan. Ini adalah hubungan paling fundamental dalam aplikasi.
*   **`Transaction` Many-to-Many `Material`**: Satu transaksi dapat menggunakan banyak jenis material (misalnya, deterjen dan pelembut), dan satu jenis material dapat digunakan di banyak transaksi. Hubungan ini diimplementasikan melalui tabel `TransactionMaterial`.
*   **`Customer` 1-to-1 `CustomerPattern`**: Setiap pelanggan memiliki satu catatan pola perilaku yang terkait dengannya. Relasi ini menghubungkan data mentah pelanggan dengan wawasan analitiknya.
*   **`Material` 1-to-1 `MaterialUsagePattern`**: Setiap material memiliki satu catatan pola penggunaan yang sesuai, yang digunakan untuk prediksi stok.