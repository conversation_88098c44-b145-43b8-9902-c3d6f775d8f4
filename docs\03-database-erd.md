# 🗄️ LaundrySeense - Entity Relationship Diagram (ERD)

## 📊 **DATABASE SCHEMA**

```mermaid
erDiagram
    Customer {
        string id PK
        string name
        string phone_number UK
        string email UK
        string address
        datetime registration_date
        datetime last_transaction_date
        float behavior_frequency_score
        float behavior_preference_score
        float behavior_seasonal_bias
    }
    
    Material {
        string id PK
        string material_name UK
        float current_stock_unit
        string unit_of_measure
        datetime last_restock_date
        float usage_rate_per_transaction
        float usage_rate_per_kg
        float minimum_stock_threshold
        float cost_per_unit
        string supplier_info
        enum category
    }
    
    Transaction {
        string id PK
        string customer_id FK
        enum service_type
        float weight_kg
        float price
        datetime transaction_date
        enum status
        enum context_weather
        enum context_day_type
        float context_seasonal_factor
        enum context_time_of_day
        datetime pickup_date
        datetime delivery_date
        string special_instructions
        float discount_applied
    }
    
    TransactionMaterial {
        string id PK
        string transaction_id FK
        string material_id FK
        float quantity_used
        float cost_at_time
    }
    
    CustomerPattern {
        string id PK
        string customer_id FK
        datetime calculated_at
        float calculated_frequency
        string frequency_trend
        int last_transaction_days_ago
        enum preferred_service_type
        float preferred_service_confidence
        float average_weight_kg
        float average_spending
        json seasonal_activity_json
        string peak_months
        string low_months
        float seasonal_variance
        float loyalty_score
        float value_score
        float predictability_score
        float confidence_score
        int data_points_count
        int analysis_period_days
    }
    
    MaterialUsagePattern {
        string id PK
        string material_id FK
        datetime calculated_at
        float average_consumption_rate
        string consumption_trend
        enum peak_usage_day_type
        enum peak_usage_time
        json seasonal_adjustment_json
        string peak_consumption_months
        string low_consumption_months
        float seasonal_multiplier
        float cost_efficiency_score
        float usage_efficiency_score
        float waste_indicator
        int predicted_days_to_empty
        boolean reorder_recommendation
        float optimal_stock_level
        float confidence_score
        int data_points_count
        int analysis_period_days
    }
    
    RevenueTrend {
        string id PK
        datetime date UK
        float daily_revenue
        int daily_transaction_count
        float daily_avg_transaction
        float daily_weight_total
        float weekly_avg_revenue
        int weekly_transaction_count
        int week_day_rank
        float monthly_avg_revenue
        int monthly_transaction_count
        int month_day_rank
        string revenue_trend
        string peak_status
        float growth_rate
        enum day_type
        enum weather_context
        float seasonal_factor
        float confidence_score
        float data_completeness
    }
    
    PatternCalculationLog {
        string id PK
        string calculation_type
        datetime started_at
        datetime completed_at
        string status
        int records_processed
        int records_updated
        string error_message
        int execution_time_ms
    }
    
    %% Relationships
    Customer ||--o{ Transaction : "has many"
    Customer ||--o| CustomerPattern : "has one"
    
    Material ||--o{ TransactionMaterial : "used in"
    Material ||--o| MaterialUsagePattern : "has one"
    
    Transaction ||--o{ TransactionMaterial : "contains"
    Transaction }o--|| Customer : "belongs to"
    
    TransactionMaterial }o--|| Transaction : "belongs to"
    TransactionMaterial }o--|| Material : "references"
    
    CustomerPattern }o--|| Customer : "analyzes"
    MaterialUsagePattern }o--|| Material : "analyzes"
```

## 🔗 **RELATIONSHIP EXPLANATIONS**

### **Core Entity Relationships**

#### **Customer ↔ Transaction (One-to-Many)**
- Setiap customer dapat memiliki banyak transactions
- Setiap transaction harus memiliki satu customer
- Cascade delete: Jika customer dihapus, semua transactions ikut terhapus

#### **Transaction ↔ TransactionMaterial (One-to-Many)**
- Setiap transaction dapat menggunakan banyak materials
- Junction table untuk many-to-many relationship
- Menyimpan quantity dan cost pada saat transaction

#### **Material ↔ TransactionMaterial (One-to-Many)**
- Setiap material dapat digunakan di banyak transactions
- Tracking usage history untuk pattern analysis
- Cost tracking untuk profitability analysis

### **Intelligence Relationships**

#### **Customer ↔ CustomerPattern (One-to-One)**
- Setiap customer memiliki satu pattern analysis
- Pattern dihitung berdasarkan transaction history
- Auto-update melalui background jobs

#### **Material ↔ MaterialUsagePattern (One-to-One)**
- Setiap material memiliki satu usage pattern
- Pattern untuk stock management dan forecasting
- Reorder recommendations berdasarkan usage trends

### **Analytics Relationships**

#### **RevenueTrend (Independent)**
- Daily aggregation dari transaction data
- Contextual data untuk business intelligence
- Trend analysis untuk forecasting

#### **PatternCalculationLog (Independent)**
- Audit trail untuk pattern calculations
- Performance monitoring untuk background jobs
- Error tracking dan debugging

## 📋 **FIELD DESCRIPTIONS**

### **Customer Table**
- **behavior_*_score**: AI-calculated scores (0.0-1.0) untuk customer segmentation
- **phone_number**: Unique identifier untuk customer lookup
- **last_transaction_date**: Untuk customer retention analysis

### **Material Table**
- **current_stock_unit**: Real-time stock level
- **usage_rate_***: Calculated rates untuk automatic consumption tracking
- **minimum_stock_threshold**: Alert threshold untuk low stock warnings

### **Transaction Table**
- **context_***: Contextual data untuk intelligent analysis
- **service_type**: Enum untuk different laundry services
- **status**: Transaction lifecycle tracking

### **Pattern Tables**
- **confidence_score**: Reliability indicator untuk pattern accuracy
- **data_points_count**: Number of records used dalam calculation
- **seasonal_***: JSON data untuk complex seasonal patterns

## 🔍 **INDEXING STRATEGY**

### **Performance Indexes**
- **Customer**: phone_number, email, last_transaction_date
- **Transaction**: customer_id, transaction_date, status, service_type
- **Material**: material_name, category, current_stock_unit
- **Patterns**: calculated_at, confidence_score

### **Composite Indexes**
- **Transaction**: (customer_id, transaction_date) untuk customer history
- **TransactionMaterial**: (transaction_id, material_id) untuk junction queries
- **RevenueTrend**: (date, peak_status) untuk analytics

## 🚀 **SCALABILITY CONSIDERATIONS**

### **Data Growth**
- **Partitioning**: RevenueTrend by date ranges
- **Archiving**: Old transactions untuk historical analysis
- **Compression**: JSON fields untuk seasonal data

### **Query Optimization**
- **Materialized Views**: Pre-calculated aggregations
- **Read Replicas**: Separate analytics queries
- **Connection Pooling**: Efficient database connections
