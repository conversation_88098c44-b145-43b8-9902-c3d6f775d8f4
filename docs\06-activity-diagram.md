# 🔄 LaundrySeense - Activity Diagrams

## 📊 **DASHBOARD ACTIVITY**

```mermaid
flowchart TD
    Start([User Opens Dashboard]) --> LoadContext{System: Load Context}
    LoadContext --> DetectTime[System: Detect Time Context]
    DetectTime --> LoadKPIs[System: Load Real-time KPIs]
    LoadKPIs --> LoadPatterns[System: Load Pattern Data]
    LoadPatterns --> GenerateInsights[System: Generate Smart Insights]
    GenerateInsights --> DisplayDashboard[System: Display Dashboard]
    DisplayDashboard --> UserView[User: View Dashboard]
    
    UserView --> CheckAlerts{User: Check Alerts?}
    CheckAlerts -->|Yes| ViewAlerts[User: View Alert Details]
    CheckAlerts -->|No| ExpandInsight{User: Expand Insight?}
    
    ViewAlerts --> TakeAction{User: Take Action?}
    TakeAction -->|Yes| NavigateToModule[User: Navigate to Module]
    TakeAction -->|No| ExpandInsight
    
    ExpandInsight -->|Yes| ShowDetails[System: Show Insight Details]
    ExpandInsight -->|No| RefreshData{Auto Refresh?}
    
    ShowDetails --> RefreshData
    RefreshData -->|Yes| LoadKPIs
    RefreshData -->|No| End([End])
    
    NavigateToModule --> End
    
    %% WebSocket Updates
    WebSocketUpdate[System: WebSocket Update] --> LoadKPIs
    
    %% Styling
    classDef userAction fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef systemAction fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class UserView,CheckAlerts,TakeAction,ExpandInsight,NavigateToModule userAction
    class LoadContext,DetectTime,LoadKPIs,LoadPatterns,GenerateInsights,DisplayDashboard,ViewAlerts,ShowDetails,WebSocketUpdate systemAction
    class CheckAlerts,TakeAction,ExpandInsight,RefreshData decision
```

## 👤 **CUSTOMER MANAGEMENT ACTIVITY**

```mermaid
flowchart TD
    Start([User Opens Customer Management]) --> LoadCustomers[System: Load Customer List]
    LoadCustomers --> DisplayList[System: Display Customer List]
    DisplayList --> UserAction{User Action}
    
    UserAction -->|Add New| OpenAddForm[User: Click Add Customer]
    UserAction -->|Search| SearchCustomer[User: Enter Search Term]
    UserAction -->|Edit| SelectCustomer[User: Select Customer]
    UserAction -->|Delete| ConfirmDelete{User: Confirm Delete?}
    
    %% Add Customer Flow
    OpenAddForm --> ShowAddForm[System: Show Enhanced Form]
    ShowAddForm --> FillForm[User: Fill Customer Data]
    FillForm --> ValidateForm[System: Real-time Validation]
    ValidateForm --> ValidationOK{Validation OK?}
    ValidationOK -->|No| ShowErrors[System: Show Validation Errors]
    ShowErrors --> FillForm
    ValidationOK -->|Yes| SubmitForm[User: Submit Form]
    SubmitForm --> SaveCustomer[System: Save to Database]
    SaveCustomer --> Success{Save Success?}
    Success -->|Yes| RefreshList[System: Refresh Customer List]
    Success -->|No| ShowError[System: Show Error Message]
    ShowError --> FillForm
    
    %% Search Flow
    SearchCustomer --> PerformSearch[System: Fuzzy Search]
    PerformSearch --> FilterList[System: Filter Customer List]
    FilterList --> DisplayFiltered[System: Display Filtered Results]
    DisplayFiltered --> UserAction
    
    %% Edit Flow
    SelectCustomer --> LoadCustomerData[System: Load Customer Data]
    LoadCustomerData --> ShowEditForm[System: Show Pre-filled Form]
    ShowEditForm --> ModifyData[User: Modify Customer Data]
    ModifyData --> ValidateEdit[System: Validate Changes]
    ValidateEdit --> EditOK{Validation OK?}
    EditOK -->|No| ShowEditErrors[System: Show Validation Errors]
    ShowEditErrors --> ModifyData
    EditOK -->|Yes| SaveChanges[User: Save Changes]
    SaveChanges --> UpdateDatabase[System: Update Database]
    UpdateDatabase --> UpdateSuccess{Update Success?}
    UpdateSuccess -->|Yes| RefreshList
    UpdateSuccess -->|No| ShowUpdateError[System: Show Error Message]
    ShowUpdateError --> ModifyData
    
    %% Delete Flow
    ConfirmDelete -->|Yes| DeleteCustomer[System: Delete from Database]
    ConfirmDelete -->|No| UserAction
    DeleteCustomer --> DeleteSuccess{Delete Success?}
    DeleteSuccess -->|Yes| RefreshList
    DeleteSuccess -->|No| ShowDeleteError[System: Show Error Message]
    ShowDeleteError --> UserAction
    
    RefreshList --> DisplayList
    End([End])
    
    %% Styling
    classDef userAction fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef systemAction fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class OpenAddForm,FillForm,SubmitForm,SearchCustomer,SelectCustomer,ModifyData,SaveChanges userAction
    class LoadCustomers,DisplayList,ShowAddForm,ValidateForm,ShowErrors,SaveCustomer,RefreshList,PerformSearch,FilterList,DisplayFiltered,LoadCustomerData,ShowEditForm,ValidateEdit,ShowEditErrors,UpdateDatabase,ShowUpdateError,DeleteCustomer,ShowDeleteError systemAction
    class UserAction,ValidationOK,Success,EditOK,UpdateSuccess,ConfirmDelete,DeleteSuccess decision
```

## 💰 **TRANSACTION MANAGEMENT ACTIVITY**

```mermaid
flowchart TD
    Start([User Opens Transaction Management]) --> LoadTransactions[System: Load Transaction List]
    LoadTransactions --> DisplayTransactions[System: Display Transaction List]
    DisplayTransactions --> UserAction{User Action}
    
    UserAction -->|Add New| OpenTransactionForm[User: Click Add Transaction]
    UserAction -->|Edit Status| SelectTransaction[User: Select Transaction]
    UserAction -->|View Details| ViewTransaction[User: View Transaction Details]
    
    %% Add Transaction Flow
    OpenTransactionForm --> ShowEnhancedForm[System: Show Enhanced Transaction Form]
    ShowEnhancedForm --> SelectCustomer[User: Search & Select Customer]
    SelectCustomer --> CustomerSearch[System: Perform Customer Search]
    CustomerSearch --> ShowCustomerResults[System: Show Search Results]
    ShowCustomerResults --> ChooseCustomer[User: Choose Customer]
    ChooseCustomer --> SelectService[User: Select Service Type]
    SelectService --> EnterWeight[User: Enter Weight]
    EnterWeight --> CalculatePrice[System: Auto-calculate Price]
    CalculatePrice --> ApplyWeatherAdjustment[System: Apply Weather Adjustment]
    ApplyWeatherAdjustment --> ShowPriceBreakdown[System: Show Price Breakdown]
    ShowPriceBreakdown --> SetSchedule[User: Set Pickup/Delivery Schedule]
    SetSchedule --> CalculateDelivery[System: Auto-calculate Delivery Date]
    CalculateDelivery --> AddInstructions[User: Add Special Instructions]
    AddInstructions --> ValidateTransaction[System: Validate Transaction Data]
    ValidateTransaction --> TransactionValid{Validation OK?}
    TransactionValid -->|No| ShowTransactionErrors[System: Show Validation Errors]
    ShowTransactionErrors --> SelectService
    TransactionValid -->|Yes| SubmitTransaction[User: Submit Transaction]
    SubmitTransaction --> SaveTransaction[System: Save to Database]
    SaveTransaction --> TransactionSaved{Save Success?}
    TransactionSaved -->|Yes| UpdateMaterialUsage[System: Update Material Usage]
    TransactionSaved -->|No| ShowSaveError[System: Show Error Message]
    ShowSaveError --> SelectService
    UpdateMaterialUsage --> RefreshTransactionList[System: Refresh Transaction List]
    
    %% Edit Status Flow
    SelectTransaction --> LoadTransactionData[System: Load Transaction Data]
    LoadTransactionData --> ShowStatusOptions[System: Show Status Options]
    ShowStatusOptions --> UpdateStatus[User: Update Status]
    UpdateStatus --> SaveStatusChange[System: Save Status Change]
    SaveStatusChange --> StatusSaved{Save Success?}
    StatusSaved -->|Yes| TriggerWebSocket[System: Trigger WebSocket Update]
    StatusSaved -->|No| ShowStatusError[System: Show Error Message]
    ShowStatusError --> ShowStatusOptions
    TriggerWebSocket --> RefreshTransactionList
    
    %% View Details Flow
    ViewTransaction --> LoadFullDetails[System: Load Full Transaction Details]
    LoadFullDetails --> ShowTransactionModal[System: Show Transaction Modal]
    ShowTransactionModal --> UserViewDetails[User: View Transaction Details]
    UserViewDetails --> CloseModal[User: Close Modal]
    CloseModal --> UserAction
    
    RefreshTransactionList --> DisplayTransactions
    End([End])
    
    %% Styling
    classDef userAction fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef systemAction fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef decision fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class OpenTransactionForm,SelectCustomer,ChooseCustomer,SelectService,EnterWeight,SetSchedule,AddInstructions,SubmitTransaction,SelectTransaction,UpdateStatus,ViewTransaction,UserViewDetails,CloseModal userAction
    class LoadTransactions,DisplayTransactions,ShowEnhancedForm,CustomerSearch,ShowCustomerResults,CalculatePrice,ApplyWeatherAdjustment,ShowPriceBreakdown,CalculateDelivery,ValidateTransaction,ShowTransactionErrors,SaveTransaction,UpdateMaterialUsage,RefreshTransactionList,LoadTransactionData,ShowStatusOptions,SaveStatusChange,TriggerWebSocket,ShowSaveError,ShowStatusError,LoadFullDetails,ShowTransactionModal systemAction
    class UserAction,TransactionValid,TransactionSaved,StatusSaved decision
```

## 📦 **MATERIAL MANAGEMENT ACTIVITY**

```mermaid
flowchart TD
    Start([User Opens Material Management]) --> LoadMaterials[System: Load Material List]
    LoadMaterials --> CalculateStockStatus[System: Calculate Stock Status]
    CalculateStockStatus --> DisplayMaterials[System: Display Material List with Status]
    DisplayMaterials --> UserAction{User Action}

    UserAction -->|Add New| OpenMaterialForm[User: Click Add Material]
    UserAction -->|Update Stock| SelectMaterial[User: Select Material]
    UserAction -->|View Usage| ViewUsagePattern[User: View Usage Pattern]

    %% Add Material Flow
    OpenMaterialForm --> ShowMaterialForm[System: Show Enhanced Material Form]
    ShowMaterialForm --> FillMaterialData[User: Fill Material Information]
    FillMaterialData --> SelectCategory[User: Select Category]
    SelectCategory --> SetStockLevel[User: Set Initial Stock Level]
    SetStockLevel --> SetThreshold[User: Set Minimum Threshold]
    SetThreshold --> CalculateInitialStatus[System: Calculate Initial Stock Status]
    CalculateInitialStatus --> ShowStatusIndicator[System: Show Status Indicator]
    ShowStatusIndicator --> SetCostInfo[User: Set Cost Information]
    SetCostInfo --> ValidateMaterial[System: Validate Material Data]
    ValidateMaterial --> MaterialValid{Validation OK?}
    MaterialValid -->|No| ShowMaterialErrors[System: Show Validation Errors]
    ShowMaterialErrors --> FillMaterialData
    MaterialValid -->|Yes| SubmitMaterial[User: Submit Material]
    SubmitMaterial --> SaveMaterial[System: Save to Database]
    SaveMaterial --> MaterialSaved{Save Success?}
    MaterialSaved -->|Yes| RefreshMaterialList[System: Refresh Material List]
    MaterialSaved -->|No| ShowMaterialSaveError[System: Show Error Message]
    ShowMaterialSaveError --> FillMaterialData

    %% Update Stock Flow
    SelectMaterial --> LoadMaterialData[System: Load Current Material Data]
    LoadMaterialData --> ShowStockForm[System: Show Stock Update Form]
    ShowStockForm --> EnterNewStock[User: Enter New Stock Level]
    EnterNewStock --> CalculateNewStatus[System: Calculate New Stock Status]
    CalculateNewStatus --> ShowNewStatus[System: Show New Status Indicator]
    ShowNewStatus --> ConfirmStockUpdate[User: Confirm Stock Update]
    ConfirmStockUpdate --> UpdateStock[System: Update Stock in Database]
    UpdateStock --> StockUpdated{Update Success?}
    StockUpdated -->|Yes| CheckLowStock[System: Check Low Stock Alert]
    StockUpdated -->|No| ShowStockError[System: Show Error Message]
    ShowStockError --> EnterNewStock
    CheckLowStock --> LowStockAlert{Low Stock?}
    LowStockAlert -->|Yes| TriggerAlert[System: Trigger Low Stock Alert]
    LowStockAlert -->|No| RefreshMaterialList
    TriggerAlert --> RefreshMaterialList

    %% View Usage Pattern Flow
    ViewUsagePattern --> LoadUsageData[System: Load Usage Pattern Data]
    LoadUsageData --> CalculateUsageMetrics[System: Calculate Usage Metrics]
    CalculateUsageMetrics --> ShowUsageChart[System: Show Usage Pattern Chart]
    ShowUsageChart --> ShowReorderRecommendation[System: Show Reorder Recommendation]
    ShowReorderRecommendation --> UserViewUsage[User: View Usage Analysis]
    UserViewUsage --> CloseUsageView[User: Close Usage View]
    CloseUsageView --> UserAction

    RefreshMaterialList --> CalculateStockStatus
    End([End])

    %% Styling
    classDef userAction fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef systemAction fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class OpenMaterialForm,FillMaterialData,SelectCategory,SetStockLevel,SetThreshold,SetCostInfo,SubmitMaterial,SelectMaterial,EnterNewStock,ConfirmStockUpdate,ViewUsagePattern,UserViewUsage,CloseUsageView userAction
    class LoadMaterials,CalculateStockStatus,DisplayMaterials,ShowMaterialForm,CalculateInitialStatus,ShowStatusIndicator,ValidateMaterial,ShowMaterialErrors,SaveMaterial,RefreshMaterialList,ShowMaterialSaveError,LoadMaterialData,ShowStockForm,CalculateNewStatus,ShowNewStatus,UpdateStock,ShowStockError,CheckLowStock,TriggerAlert,LoadUsageData,CalculateUsageMetrics,ShowUsageChart,ShowReorderRecommendation systemAction
    class UserAction,MaterialValid,MaterialSaved,StockUpdated,LowStockAlert decision
```

## 📈 **ANALYTICS ACTIVITY**

```mermaid
flowchart TD
    Start([User Opens Analytics]) --> LoadAnalyticsData[System: Load Analytics Data]
    LoadAnalyticsData --> CalculateMetrics[System: Calculate Business Metrics]
    CalculateMetrics --> GenerateCharts[System: Generate Charts & Graphs]
    GenerateCharts --> DisplayAnalytics[System: Display Analytics Dashboard]
    DisplayAnalytics --> UserAction{User Action}

    UserAction -->|Change Period| SelectPeriod[User: Select Time Period]
    UserAction -->|View Details| SelectMetric[User: Select Specific Metric]
    UserAction -->|Export Data| ExportData[User: Click Export]

    %% Change Period Flow
    SelectPeriod --> FilterByPeriod[System: Filter Data by Period]
    FilterByPeriod --> RecalculateMetrics[System: Recalculate Metrics]
    RecalculateMetrics --> UpdateCharts[System: Update Charts]
    UpdateCharts --> DisplayUpdatedAnalytics[System: Display Updated Analytics]
    DisplayUpdatedAnalytics --> UserAction

    %% View Details Flow
    SelectMetric --> LoadDetailedData[System: Load Detailed Data]
    LoadDetailedData --> ShowDetailModal[System: Show Detail Modal]
    ShowDetailModal --> UserViewDetail[User: View Detailed Analysis]
    UserViewDetail --> CloseDetailModal[User: Close Modal]
    CloseDetailModal --> UserAction

    %% Export Flow
    ExportData --> PrepareExportData[System: Prepare Export Data]
    PrepareExportData --> GenerateExportFile[System: Generate Export File]
    GenerateExportFile --> DownloadFile[System: Trigger File Download]
    DownloadFile --> UserAction

    End([End])

    %% Styling
    classDef userAction fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef systemAction fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class SelectPeriod,SelectMetric,ExportData,UserViewDetail,CloseDetailModal userAction
    class LoadAnalyticsData,CalculateMetrics,GenerateCharts,DisplayAnalytics,FilterByPeriod,RecalculateMetrics,UpdateCharts,DisplayUpdatedAnalytics,LoadDetailedData,ShowDetailModal,PrepareExportData,GenerateExportFile,DownloadFile systemAction
    class UserAction decision
```

## 📝 **ACTIVITY EXPLANATIONS**

### **Dashboard Activity**
- **Real-time Updates**: WebSocket connection memungkinkan auto-refresh data
- **Context Awareness**: System mendeteksi time context untuk adaptive insights
- **User Interaction**: Progressive disclosure dengan expandable insight cards
- **Alert Integration**: Seamless navigation dari alerts ke relevant modules

### **Customer Management Activity**
- **Enhanced Forms**: Real-time validation dengan user-friendly error messages
- **Search Functionality**: Fuzzy search untuk easy customer discovery
- **Data Integrity**: Multi-layer validation (client-side dan server-side)
- **Error Handling**: Comprehensive error handling dengan user feedback

### **Transaction Management Activity**
- **Smart Forms**: Auto-calculation pricing berdasarkan service, weight, dan weather
- **Customer Integration**: Seamless customer search dan selection
- **Scheduling Intelligence**: Auto-calculation delivery dates berdasarkan service type
- **Real-time Updates**: WebSocket updates untuk status changes
- **Material Tracking**: Automatic material usage updates

### **Material Management Activity**
- **Stock Status Calculation**: Real-time stock status dengan visual indicators
- **Alert System**: Automatic low stock alerts dengan recommendations
- **Usage Pattern Analysis**: Intelligent usage forecasting
- **Reorder Intelligence**: Smart reorder recommendations berdasarkan patterns

### **Analytics Activity**
- **Dynamic Filtering**: Real-time data filtering berdasarkan time periods
- **Interactive Charts**: Detailed drill-down capabilities
- **Export Functionality**: Data export untuk external analysis
- **Performance Optimization**: Efficient data loading dan calculation

## 🎯 **KEY DESIGN PRINCIPLES**

### **User-Centric Design**
- Clear separation antara user actions dan system responses
- Immediate feedback untuk semua user interactions
- Progressive disclosure untuk complex information

### **Error Handling**
- Graceful error handling dengan informative messages
- Validation di multiple layers (client dan server)
- Recovery paths untuk error conditions

### **Performance Optimization**
- Efficient data loading dengan pagination
- Real-time updates hanya untuk relevant data
- Background processing untuk heavy calculations

### **Intelligence Integration**
- Context-aware system behavior
- Automatic calculations dan recommendations
- Pattern-based insights dan alerts
