# 🏗️ LaundrySeense - Class Diagram

## 📐 **SYSTEM CLASS STRUCTURE**

```mermaid
classDiagram
    %% Frontend Components
    class DashboardPage {
        -insights: GeneratedInsight[]
        -expandedCard: string
        +handleToggleCard(id: string)
        +render()
    }
    
    class DashboardContext {
        -currentTime: Date
        -contextMode: ContextMode
        -kpis: Kpi[]
        -insights: GeneratedInsight[]
        -realTimeKpis: RealTimeData
        +setContextMode(mode: ContextMode)
        +loadRealTimeData()
        +generateInsights()
    }
    
    class EnhancedTransactionForm {
        -formData: TransactionFormData
        -customerSearch: string
        -selectedCustomer: Customer
        -errors: Record<string, string>
        +validateForm(): boolean
        +handleCustomerSelect(customer: Customer)
        +calculatePrice(): number
        +handleSubmit(data: TransactionFormData)
    }
    
    class EnhancedCustomerForm {
        -formData: CustomerFormData
        -phoneFormatted: string
        +validateForm(): boolean
        +formatPhone(phone: string): string
        +handleSubmit(data: CustomerFormData)
    }
    
    class EnhancedMaterialForm {
        -formData: MaterialFormData
        -stockStatus: StockStatus
        +validateForm(): boolean
        +calculateStockStatus(): StockStatus
        +formatCurrency(value: number): string
        +handleSubmit(data: MaterialFormData)
    }
    
    %% API Route Classes
    class CustomerAPI {
        +GET(): Promise<Customer[]>
        +POST(data: CustomerData): Promise<Customer>
        +PUT(id: string, data: CustomerData): Promise<Customer>
        +DELETE(id: string): Promise<void>
        -validateCustomerData(data: any): CustomerData
    }
    
    class TransactionAPI {
        +GET(): Promise<Transaction[]>
        +POST(data: TransactionData): Promise<Transaction>
        +PUT(id: string, data: TransactionData): Promise<Transaction>
        +DELETE(id: string): Promise<void>
        -validateTransactionData(data: any): TransactionData
        -checkCustomerExists(customerId: string): Promise<boolean>
    }
    
    class MaterialAPI {
        +GET(): Promise<Material[]>
        +POST(data: MaterialData): Promise<Material>
        +PUT(id: string, data: MaterialData): Promise<Material>
        +DELETE(id: string): Promise<void>
        -validateMaterialData(data: any): MaterialData
    }
    
    class AnalyticsAPI {
        +GET(range: string): Promise<AnalyticsData>
        -calculateRevenueData(): Promise<RevenueData>
        -calculateCustomerMetrics(): Promise<CustomerMetrics>
        -calculateMaterialUsage(): Promise<MaterialUsage>
    }
    
    %% Data Models
    class Customer {
        +id: string
        +name: string
        +phone_number: string
        +email: string
        +address: string
        +registration_date: Date
        +last_transaction_date: Date
        +behavior_frequency_score: number
        +behavior_preference_score: number
        +behavior_seasonal_bias: number
        +transactions: Transaction[]
        +customer_pattern: CustomerPattern
    }
    
    class Transaction {
        +id: string
        +customer_id: string
        +service_type: ServiceType
        +weight_kg: number
        +price: number
        +transaction_date: Date
        +status: TransactionStatus
        +context_weather: WeatherContext
        +context_day_type: DayType
        +context_time_of_day: TimeOfDay
        +pickup_date: Date
        +delivery_date: Date
        +special_instructions: string
        +discount_applied: number
        +customer: Customer
        +transaction_materials: TransactionMaterial[]
    }
    
    class Material {
        +id: string
        +material_name: string
        +current_stock_unit: number
        +unit_of_measure: string
        +usage_rate_per_transaction: number
        +usage_rate_per_kg: number
        +minimum_stock_threshold: number
        +cost_per_unit: number
        +supplier_info: string
        +category: MaterialCategory
        +transaction_materials: TransactionMaterial[]
        +usage_pattern: MaterialUsagePattern
    }
    
    class TransactionMaterial {
        +id: string
        +transaction_id: string
        +material_id: string
        +quantity_used: number
        +cost_at_time: number
        +transaction: Transaction
        +material: Material
    }
    
    %% Intelligence Classes
    class SmartInsightGenerator {
        +generateInsights(input: InsightGenerationInput): InsightResult
        -analyzeRevenueTrends(data: RealTimeData): GeneratedInsight[]
        -analyzeCustomerPatterns(data: PatternData): GeneratedInsight[]
        -analyzeMaterialUsage(data: RealTimeData): GeneratedInsight[]
        -analyzeOperationalEfficiency(data: RealTimeData): GeneratedInsight[]
    }
    
    class ContextDetector {
        +detectContext(timestamp: Date): CurrentContextObject
        -detectTimeContext(timestamp: Date): TimeContext
        -detectBusinessCycle(): BusinessCycle
        -detectUserBehavior(): UserBehavior
        -detectDataQuality(): DataQuality
    }
    
    class PatternCalculationJob {
        +calculateCustomerPatterns(): Promise<void>
        +calculateMaterialPatterns(): Promise<void>
        +calculateRevenueTrends(): Promise<void>
        -processCustomerData(customer: Customer): CustomerPattern
        -processMaterialData(material: Material): MaterialUsagePattern
        -processRevenueData(date: Date): RevenueTrend
    }
    
    %% Pattern Analysis Classes
    class CustomerPattern {
        +id: string
        +customer_id: string
        +calculated_frequency: number
        +frequency_trend: string
        +preferred_service_type: ServiceType
        +average_spending: number
        +loyalty_score: number
        +confidence_score: number
        +customer: Customer
    }
    
    class MaterialUsagePattern {
        +id: string
        +material_id: string
        +average_consumption_rate: number
        +consumption_trend: string
        +predicted_days_to_empty: number
        +reorder_recommendation: boolean
        +confidence_score: number
        +material: Material
    }
    
    class RevenueTrend {
        +id: string
        +date: Date
        +daily_revenue: number
        +daily_transaction_count: number
        +revenue_trend: string
        +peak_status: string
        +growth_rate: number
        +confidence_score: number
    }
    
    %% Custom Hooks
    class useApi {
        -loading: boolean
        -error: string
        +request(url: string, method: string, body?: any): Promise<any>
        +get(url: string): Promise<any>
        +post(url: string, body: any): Promise<any>
        +put(url: string, body: any): Promise<any>
        +delete(url: string): Promise<any>
    }
    
    class useCustomers {
        +getCustomers(params?: SearchParams): Promise<Customer[]>
        +createCustomer(customer: CustomerData): Promise<Customer>
        +updateCustomer(id: string, customer: CustomerData): Promise<Customer>
        +deleteCustomer(id: string): Promise<void>
    }
    
    class useTransactions {
        +getTransactions(params?: SearchParams): Promise<Transaction[]>
        +createTransaction(transaction: TransactionData): Promise<Transaction>
        +updateTransaction(id: string, transaction: TransactionData): Promise<Transaction>
        +deleteTransaction(id: string): Promise<void>
    }
    
    class useMaterials {
        +getMaterials(params?: SearchParams): Promise<Material[]>
        +createMaterial(material: MaterialData): Promise<Material>
        +updateMaterial(id: string, material: MaterialData): Promise<Material>
        +deleteMaterial(id: string): Promise<void>
    }
    
    %% Relationships
    DashboardPage --> DashboardContext : uses
    DashboardContext --> SmartInsightGenerator : uses
    DashboardContext --> ContextDetector : uses
    
    EnhancedTransactionForm --> useTransactions : uses
    EnhancedTransactionForm --> useCustomers : uses
    EnhancedCustomerForm --> useCustomers : uses
    EnhancedMaterialForm --> useMaterials : uses
    
    useCustomers --> CustomerAPI : calls
    useTransactions --> TransactionAPI : calls
    useMaterials --> MaterialAPI : calls
    
    CustomerAPI --> Customer : manages
    TransactionAPI --> Transaction : manages
    MaterialAPI --> Material : manages
    
    Customer ||--o{ Transaction : has
    Transaction ||--o{ TransactionMaterial : contains
    Material ||--o{ TransactionMaterial : used_in
    
    Customer ||--|| CustomerPattern : analyzed_by
    Material ||--|| MaterialUsagePattern : analyzed_by
    
    SmartInsightGenerator --> CustomerPattern : analyzes
    SmartInsightGenerator --> MaterialUsagePattern : analyzes
    SmartInsightGenerator --> RevenueTrend : analyzes
    
    PatternCalculationJob --> CustomerPattern : creates
    PatternCalculationJob --> MaterialUsagePattern : creates
    PatternCalculationJob --> RevenueTrend : creates
```

## 🔍 **CLASS DESCRIPTIONS**

### **Frontend Component Classes**

#### **DashboardPage**
- **Purpose**: Main dashboard component yang menampilkan real-time metrics dan insights
- **Key Methods**:
  - `handleToggleCard()`: Mengatur expand/collapse insight cards
  - `render()`: Rendering dashboard dengan context-aware layout
- **State Management**: Menggunakan DashboardContext untuk global state

#### **Enhanced Form Classes**
- **EnhancedTransactionForm**: Smart form untuk transaction creation dengan autocomplete customer search, real-time price calculation, dan automatic scheduling
- **EnhancedCustomerForm**: Customer form dengan phone formatting dan validation
- **EnhancedMaterialForm**: Material form dengan stock status calculation dan cost analysis

### **API Route Classes**

#### **CustomerAPI, TransactionAPI, MaterialAPI**
- **Purpose**: RESTful API endpoints untuk CRUD operations
- **Pattern**: Consistent structure dengan GET, POST, PUT, DELETE methods
- **Validation**: Menggunakan Zod schemas untuk type-safe validation
- **Error Handling**: Comprehensive error responses dengan detailed messages

#### **AnalyticsAPI**
- **Purpose**: Complex analytics calculations dan aggregations
- **Methods**: Revenue analysis, customer metrics, material usage patterns
- **Performance**: Optimized queries dengan proper indexing

### **Data Model Classes**

#### **Core Entities**
- **Customer**: Customer information dengan behavioral scores
- **Transaction**: Transaction data dengan contextual intelligence
- **Material**: Inventory management dengan usage tracking
- **TransactionMaterial**: Junction table untuk many-to-many relationship

#### **Intelligence Entities**
- **CustomerPattern**: AI-calculated customer behavior patterns
- **MaterialUsagePattern**: Material consumption analysis dan forecasting
- **RevenueTrend**: Daily revenue analysis dengan trend detection

### **Intelligence Engine Classes**

#### **SmartInsightGenerator**
- **Purpose**: AI-powered insight generation berdasarkan data patterns
- **Methods**: Specialized analysis untuk revenue, customer, material, dan operational insights
- **Input**: Real-time data dan pattern data
- **Output**: Actionable business insights

#### **ContextDetector**
- **Purpose**: Mendeteksi business context berdasarkan time, data quality, dan patterns
- **Context Types**: Time context, business cycle, user behavior, data quality
- **Usage**: Mempengaruhi insight generation dan UI behavior

#### **PatternCalculationJob**
- **Purpose**: Background job untuk calculating patterns dari raw data
- **Scheduling**: Cron jobs untuk daily dan weekly calculations
- **Processing**: Batch processing untuk performance optimization

### **Custom Hook Classes**

#### **useApi**
- **Purpose**: Base hook untuk API communications
- **Features**: Loading states, error handling, request abstraction
- **Methods**: Generic HTTP methods dengan consistent interface

#### **Specialized Hooks**
- **useCustomers, useTransactions, useMaterials**: Entity-specific hooks
- **Features**: CRUD operations, search functionality, pagination
- **Integration**: Direct integration dengan corresponding API routes

## 🔗 **RELATIONSHIP PATTERNS**

### **Component → Hook → API Pattern**
```
EnhancedTransactionForm → useTransactions → TransactionAPI → Transaction
```
- Clean separation of concerns
- Reusable business logic dalam hooks
- Type-safe data flow

### **Intelligence Processing Pattern**
```
PatternCalculationJob → Pattern Entities → SmartInsightGenerator → Dashboard
```
- Background processing untuk heavy calculations
- Real-time insight generation
- Context-aware display

### **Data Flow Pattern**
```
User Input → Form Validation → API Validation → Database → Pattern Analysis → Insights
```
- Multi-layer validation untuk data integrity
- Automatic pattern detection
- Intelligent feedback loop

## 🎯 **DESIGN PRINCIPLES**

### **Single Responsibility**
- Setiap class memiliki satu tanggung jawab yang jelas
- Separation antara UI, business logic, dan data access

### **Dependency Injection**
- Hooks sebagai dependency injection untuk components
- API classes sebagai service layer

### **Type Safety**
- TypeScript interfaces untuk semua data structures
- Zod validation untuk runtime type checking

### **Scalability**
- Modular architecture untuk easy extension
- Pattern-based design untuk consistency
- Performance optimization melalui proper indexing dan caching
