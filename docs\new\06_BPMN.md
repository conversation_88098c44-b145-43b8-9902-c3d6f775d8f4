# 6. Business Process Model and Notation (BPMN)

Dokumen ini menyajikan model proses bisnis utama dalam aplikasi `laundrysense` menggunakan notasi BPMN. Diagram ini membantu memvisualisasikan alur kerja operasional dan teknis.

## 6.1. Alur 1: Proses Transaksi Harian

Proses ini menggambarkan alur kerja standar dari saat pelanggan datang hingga transaksi selesai dan cucian diambil. Ini adalah proses inti yang dihadapi oleh karyawan setiap hari.

**Aktor:** Pelanggan, Karyawan, Sistem

```mermaid
graph TD
    A[Start: Pelanggan Datang] --> B{Catat Transaksi};
    B --> C[Proses <PERSON>];
    C --> D{Perbarui Status: 'Selesai Dicuci'};
    D --> E[Notifikasi Pelanggan (Otomatis/Manual)];
    E --> F{Pelanggan Datang untuk Mengambil};
    F --> G[Serahkan Cucian & Terima Pembayaran];
    G --> H{Perbarui Status: 'Diambil'};
    H --> I[End: Transaksi Selesai];

    subgraph "Sistem (Aplikasi laundrysense)"
        B; D; H;
    end

    subgraph "Karyawan (Tindakan Fisik & Input)"
        C; G;
    end

    subgraph "Pelanggan"
        A; F;
    end
```

**Deskripsi Langkah-langkah:**
1.  **Pelanggan Datang**: Proses dimulai ketika pelanggan tiba di laundry.
2.  **Catat Transaksi**: Karyawan menggunakan aplikasi untuk membuat catatan transaksi baru, memasukkan detail pelanggan dan item cucian. Sistem menyimpan data ini.
3.  **Proses Cucian**: Karyawan melakukan pekerjaan fisik mencuci, mengeringkan, dan menyetrika pakaian.
4.  **Perbarui Status: 'Selesai Dicuci'**: Setelah selesai, karyawan memperbarui status transaksi di aplikasi.
5.  **Notifikasi Pelanggan**: Sistem dapat mengirimkan notifikasi (misalnya, SMS atau WhatsApp, jika diimplementasikan) atau karyawan memberitahu pelanggan secara manual.
6.  **Pelanggan Datang untuk Mengambil**: Pelanggan kembali untuk mengambil cucian mereka.
7.  **Serahkan Cucian & Terima Pembayaran**: Karyawan menyerahkan cucian dan menyelesaikan proses pembayaran.
8.  **Perbarui Status: 'Diambil'**: Karyawan menandai transaksi sebagai selesai sepenuhnya di dalam sistem.
9.  **Transaksi Selesai**: Alur kerja untuk transaksi ini berakhir.

---

## 6.2. Alur 2: Proses Analisis Latar Belakang

Proses ini berjalan secara otomatis di latar belakang (biasanya di malam hari) untuk menganalisis data historis dan memperbarui tabel pola. Ini adalah proses teknis yang tidak terlihat oleh pengguna tetapi sangat penting untuk fungsionalitas cerdas aplikasi.

**Aktor:** Sistem (Cron Job, PatternAnalysisService)

```mermaid
graph TD
    A(Start: Timer Cron Job Terpicu) --> B[Memulai PatternAnalysisService];
    B --> C{Membaca Data Transaksional Historis};
    C --> D[Menghitung Pola Perilaku Pelanggan];
    D --> E[Menghitung Pola Penggunaan Material];
    E --> F[Menghitung Tren Pendapatan];
    F --> G{Menyimpan Hasil ke Tabel Analitik};
    G --> H[Mencatat Log Eksekusi];
    H --> I(End: Analisis Selesai);

    subgraph "Database"
        C; G;
    end

    subgraph "PatternAnalysisService (src/lib/pattern-analysis)"
        B; D; E; F; H;
    end
```

**Deskripsi Langkah-langkah:**
1.  **Timer Cron Job Terpicu**: Proses dimulai secara otomatis oleh `node-cron` pada jadwal yang telah ditentukan (misalnya, setiap jam 2 pagi).
2.  **Memulai PatternAnalysisService**: Cron job menjalankan skrip yang menginisialisasi layanan analisis pola.
3.  **Membaca Data Transaksional Historis**: Layanan melakukan query ke database untuk mengambil data dari tabel `Transaction`, `Customer`, dan `TransactionMaterial` dari periode waktu tertentu (misalnya, 90 hari terakhir).
4.  **Menghitung Pola**: Layanan melakukan agregasi dan perhitungan statistik pada data yang diambil untuk menemukan:
    *   Frekuensi kunjungan rata-rata per pelanggan.
    *   Nilai transaksi rata-rata per pelanggan.
    *   Konsumsi mingguan rata-rata per material.
    *   Pendapatan harian dan mingguan.
5.  **Menyimpan Hasil ke Tabel Analitik**: Hasil perhitungan di atas kemudian disimpan (diperbarui atau dibuat baru) ke dalam tabel `CustomerPattern`, `MaterialUsagePattern`, dan `RevenueTrend`.
6.  **Mencatat Log Eksekusi**: Setelah proses selesai (baik berhasil maupun gagal), sebuah catatan dibuat di tabel `PatternCalculationLog` yang berisi waktu mulai, waktu selesai, dan status.
7.  **Analisis Selesai**: Alur kerja otomatis ini berakhir dan akan menunggu pemicu berikutnya.