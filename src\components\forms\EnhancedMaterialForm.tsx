"use client";

import React, { useState, useEffect } from 'react';
import { Package, DollarSign, Ruler, Tag, AlertCircle, CheckCircle2, TrendingUp, AlertTriangle } from 'lucide-react';

interface MaterialFormData {
  material_name: string;
  category: string;
  cost_per_unit: number;
  unit_of_measure: string;
  current_stock_unit: number; // Match API field name
  minimum_stock_threshold: number;
  supplier_info: string;
  usage_rate_per_transaction?: number;
  usage_rate_per_kg?: number;
}

interface EnhancedMaterialFormProps {
  initialData?: Partial<MaterialFormData>;
  onSubmit: (data: MaterialFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  isEditing?: boolean;
}

const MATERIAL_CATEGORIES = [
  { value: 'DETERGENT', label: '🧴 Deterjen', icon: '🧴' },
  { value: 'SOFTENER', label: '🌸 Pelembut', icon: '🌸' },
  { value: 'BLEACH', label: '⚪ Pemutih', icon: '⚪' },
  { value: 'STARCH', label: '✨ Kanji', icon: '✨' },
  { value: 'PACKAGING', label: '📦 Kemasan', icon: '📦' },
  { value: 'CLEANING', label: '🧽 Pembersih', icon: '🧽' },
  { value: 'OTHER', label: '📋 Lainnya', icon: '📋' },
];

const UNIT_OPTIONS = [
  { value: 'kg', label: 'Kilogram (kg)', symbol: 'kg' },
  { value: 'liter', label: 'Liter (L)', symbol: 'L' },
  { value: 'pcs', label: 'Pieces (pcs)', symbol: 'pcs' },
  { value: 'box', label: 'Box', symbol: 'box' },
  { value: 'bottle', label: 'Botol', symbol: 'btl' },
  { value: 'pack', label: 'Pack', symbol: 'pack' },
];

const EnhancedMaterialForm: React.FC<EnhancedMaterialFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  isEditing = false,
}) => {
  const [formData, setFormData] = useState<MaterialFormData>({
    material_name: '',
    category: '',
    cost_per_unit: 0,
    unit_of_measure: '',
    current_stock_unit: 0,
    minimum_stock_threshold: 0,
    supplier_info: '',
    usage_rate_per_transaction: 0,
    usage_rate_per_kg: 0,
    ...initialData,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [stockStatus, setStockStatus] = useState<'safe' | 'warning' | 'critical'>('safe');

  // Calculate stock status
  useEffect(() => {
    if (formData.current_stock_unit <= 0) {
      setStockStatus('critical');
    } else if (formData.current_stock_unit <= formData.minimum_stock_threshold) {
      setStockStatus('warning');
    } else {
      setStockStatus('safe');
    }
  }, [formData.current_stock_unit, formData.minimum_stock_threshold]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Material name validation
    if (!formData.material_name.trim()) {
      newErrors.material_name = 'Nama material wajib diisi';
    } else if (formData.material_name.trim().length < 2) {
      newErrors.material_name = 'Nama material minimal 2 karakter';
    }

    // Category validation
    if (!formData.category) {
      newErrors.category = 'Kategori wajib dipilih';
    }

    // Cost validation
    if (formData.cost_per_unit <= 0) {
      newErrors.cost_per_unit = 'Harga per unit harus lebih dari 0';
    }

    // Unit validation
    if (!formData.unit_of_measure) {
      newErrors.unit_of_measure = 'Satuan wajib dipilih';
    }

    // Stock validation
    if (formData.current_stock_unit < 0) {
      newErrors.current_stock_unit = 'Stok tidak boleh negatif';
    }

    if (formData.minimum_stock_threshold < 0) {
      newErrors.minimum_stock_threshold = 'Batas minimum stok tidak boleh negatif';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    const cleanedData = {
      ...formData,
      material_name: formData.material_name.trim(),
      supplier_info: formData.supplier_info.trim() || null,
    };
    
    await onSubmit(cleanedData);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const getStockStatusColor = () => {
    switch (stockStatus) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getStockStatusIcon = () => {
    switch (stockStatus) {
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      case 'warning': return <AlertCircle className="w-4 h-4" />;
      default: return <CheckCircle2 className="w-4 h-4" />;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-responsive max-w-6xl mx-auto p-responsive">
      {/* Header */}
      <div className="text-center pb-6 border-b border-gray-200">
        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Package className="w-8 h-8 text-purple-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">
          {isEditing ? 'Edit Material' : 'Tambah Material Baru'}
        </h2>
        <p className="text-gray-600 mt-2">
          {isEditing ? 'Perbarui informasi material' : 'Lengkapi informasi material baru'}
        </p>
      </div>

      {/* Basic Information */}
      <div className="card">
        <div className="flex items-center gap-2 mb-4">
          <Tag className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Informasi Dasar</h3>
        </div>
        
        <div className="mobile-grid mobile-grid-md gap-6">
          {/* Material Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nama Material <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                value={formData.material_name}
                onChange={(e) => setFormData(prev => ({ ...prev, material_name: e.target.value }))}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Masukkan nama material"
              />
            </div>
            {errors.material_name && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.material_name}
              </div>
            )}
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Kategori <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="">Pilih kategori</option>
              {MATERIAL_CATEGORIES.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
            {errors.category && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.category}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Pricing & Units */}
      <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <div className="flex items-center gap-2 mb-4">
          <DollarSign className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">Harga & Satuan</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Cost per unit */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Harga per Unit <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">Rp</span>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.cost_per_unit}
                onChange={(e) => setFormData(prev => ({ ...prev, cost_per_unit: parseFloat(e.target.value) || 0 }))}
                className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="0.00"
              />
            </div>
            {formData.cost_per_unit > 0 && (
              <div className="mt-1 text-sm text-gray-600">
                {formatCurrency(formData.cost_per_unit)}
              </div>
            )}
            {errors.cost_per_unit && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.cost_per_unit}
              </div>
            )}
          </div>

          {/* Unit of measure */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Satuan <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Ruler className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
                value={formData.unit_of_measure}
                onChange={(e) => setFormData(prev => ({ ...prev, unit_of_measure: e.target.value }))}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="">Pilih satuan</option>
                {UNIT_OPTIONS.map((unit) => (
                  <option key={unit.value} value={unit.value}>
                    {unit.label}
                  </option>
                ))}
              </select>
            </div>
            {errors.unit_of_measure && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.unit_of_measure}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Stock Management */}
      <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <div className="flex items-center gap-2 mb-4">
          <TrendingUp className="w-5 h-5 text-orange-600" />
          <h3 className="text-lg font-semibold text-gray-900">Manajemen Stok</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Current Stock */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Stok Saat Ini <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.1"
                min="0"
                value={formData.current_stock_unit}
                onChange={(e) => setFormData(prev => ({ ...prev, current_stock_unit: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="0"
              />
              {formData.unit_of_measure && (
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                  {UNIT_OPTIONS.find(u => u.value === formData.unit_of_measure)?.symbol}
                </span>
              )}
            </div>
            {errors.current_stock_unit && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.current_stock_unit}
              </div>
            )}
          </div>

          {/* Minimum Stock Threshold */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Batas Minimum Stok <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.1"
                min="0"
                value={formData.minimum_stock_threshold}
                onChange={(e) => setFormData(prev => ({ ...prev, minimum_stock_threshold: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="0"
              />
              {formData.unit_of_measure && (
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                  {UNIT_OPTIONS.find(u => u.value === formData.unit_of_measure)?.symbol}
                </span>
              )}
            </div>
            {errors.minimum_stock_threshold && (
              <div className="mt-1 flex items-center gap-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.minimum_stock_threshold}
              </div>
            )}
          </div>
        </div>

        {/* Stock Status Indicator */}
        {formData.current_stock_unit > 0 && formData.minimum_stock_threshold > 0 && (
          <div className={`mt-4 p-4 rounded-lg border ${getStockStatusColor()}`}>
            <div className="flex items-center gap-2">
              {getStockStatusIcon()}
              <span className="font-medium">
                Status Stok: {
                  stockStatus === 'critical' ? 'Kritis - Stok Habis!' :
                  stockStatus === 'warning' ? 'Peringatan - Stok Rendah' :
                  'Aman - Stok Mencukupi'
                }
              </span>
            </div>
            <div className="mt-2 text-sm">
              {stockStatus === 'critical' && 'Segera lakukan pembelian material ini!'}
              {stockStatus === 'warning' && 'Pertimbangkan untuk menambah stok dalam waktu dekat.'}
              {stockStatus === 'safe' && 'Stok dalam kondisi baik.'}
            </div>
          </div>
        )}
      </div>

      {/* Additional Information */}
      <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Informasi Tambahan</h3>

        <div className="space-y-4">
          {/* Supplier Info */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Informasi Supplier
            </label>
            <textarea
              value={formData.supplier_info}
              onChange={(e) => setFormData(prev => ({ ...prev, supplier_info: e.target.value }))}
              rows={2}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
              placeholder="Nama supplier, kontak, atau informasi pembelian lainnya"
            />
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Catatan
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
              placeholder="Catatan tambahan tentang material ini"
            />
          </div>
        </div>
      </div>

      {/* Cost Summary */}
      {formData.cost_per_unit > 0 && formData.current_stock_unit > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200 p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Ringkasan Nilai</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(formData.cost_per_unit)}
              </div>
              <div className="text-sm text-gray-600 mt-1">Harga per {formData.unit_of_measure}</div>
            </div>

            <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
              <div className="text-2xl font-bold text-green-600">
                {formData.current_stock_unit} {UNIT_OPTIONS.find(u => u.value === formData.unit_of_measure)?.symbol}
              </div>
              <div className="text-sm text-gray-600 mt-1">Stok Tersedia</div>
            </div>

            <div className="text-center p-4 bg-white rounded-lg border border-gray-200">
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(formData.cost_per_unit * formData.current_stock_unit)}
              </div>
              <div className="text-sm text-gray-600 mt-1">Total Nilai Stok</div>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="mobile-stack mobile-stack-sm pt-6">
        <button
          type="button"
          onClick={onCancel}
          className="flex-1 px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
        >
          Batal
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center gap-2"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Menyimpan...
            </>
          ) : (
            <>
              <CheckCircle2 className="w-4 h-4" />
              {isEditing ? 'Update Material' : 'Simpan Material'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default EnhancedMaterialForm;
