# LaundrySense Deployment Guide

## 📋 **OVERVIEW**

Panduan lengkap untuk deployment LaundrySense dari development hingga production environment.

**Current Version**: 1.0  
**Supported Environments**: Development, Staging, Production  
**Deployment Strategy**: Blue-Green Deployment (planned)

---

## 🔧 **SYSTEM REQUIREMENTS**

### **Minimum Requirements**
- **Node.js**: 18.0.0 or higher
- **npm**: 9.0.0 or higher
- **MySQL**: 8.0 or higher
- **Memory**: 2GB RAM
- **Storage**: 10GB available space
- **Network**: Stable internet connection

### **Recommended Requirements**
- **Node.js**: 20.0.0 LTS
- **npm**: 10.0.0 or higher
- **MySQL**: 8.0.35 or higher
- **Memory**: 4GB RAM
- **Storage**: 20GB SSD
- **Network**: High-speed internet connection

---

## 🏗️ **ENVIRONMENT SETUP**

### **Development Environment**

#### **1. Prerequisites Installation**
```bash
# Install Node.js (using nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# Install MySQL
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# macOS
brew install mysql

# Windows
# Download from https://dev.mysql.com/downloads/mysql/
```

#### **2. Project Setup**
```bash
# Clone repository
git clone <repository-url>
cd laundrysense

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env
```

#### **3. Environment Variables**
```bash
# .env file
DATABASE_URL="mysql://username:password@localhost:3306/laundrysense"
WEBSOCKET_PORT=3001
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3000
```

#### **4. Database Setup**
```bash
# Create database
mysql -u root -p
CREATE DATABASE laundrysense;
EXIT;

# Generate Prisma client
npx prisma generate

# Push database schema
npx prisma db push

# Seed database (optional)
npm run seed
```

#### **5. Start Development Server**
```bash
# Start Next.js development server
npm run dev

# Start WebSocket server (in separate terminal)
npm run websocket
```

### **Staging Environment**

#### **1. Server Setup**
```bash
# Ubuntu/Debian server setup
sudo apt update
sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MySQL
sudo apt install mysql-server
sudo mysql_secure_installation

# Install PM2 for process management
sudo npm install -g pm2
```

#### **2. Application Deployment**
```bash
# Clone and setup application
git clone <repository-url>
cd laundrysense
npm ci --only=production

# Setup environment
cp .env.example .env
# Edit .env with staging configuration

# Build application
npm run build

# Setup database
npx prisma generate
npx prisma db push
```

#### **3. Process Management**
```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'laundrysense-web',
      script: 'npm',
      args: 'start',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    },
    {
      name: 'laundrysense-websocket',
      script: 'npm',
      args: 'run websocket',
      env: {
        NODE_ENV: 'production',
        WEBSOCKET_PORT: 3001
      }
    }
  ]
};
EOF

# Start applications
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### **Production Environment**

#### **1. Docker Setup**
```dockerfile
# Dockerfile
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### **2. Docker Compose**
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://user:password@db:3306/laundrysense
    depends_on:
      - db
    restart: unless-stopped

  websocket:
    build: .
    command: npm run websocket
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - WEBSOCKET_PORT=3001
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=laundrysense
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mysql_data:
```

#### **3. Nginx Configuration**
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:3000;
    }

    upstream websocket {
        server websocket:3001;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /ws {
            proxy_pass http://websocket;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }
    }
}
```

---

## 🚀 **DEPLOYMENT PROCESS**

### **Manual Deployment**

#### **1. Pre-deployment Checklist**
- [ ] Code review completed
- [ ] Tests passing
- [ ] Environment variables configured
- [ ] Database migrations ready
- [ ] Backup created
- [ ] Monitoring alerts configured

#### **2. Deployment Steps**
```bash
# 1. Pull latest code
git pull origin main

# 2. Install dependencies
npm ci --only=production

# 3. Run database migrations
npx prisma db push

# 4. Build application
npm run build

# 5. Restart services
pm2 restart all

# 6. Verify deployment
curl -f http://localhost:3000/api/health || exit 1
```

### **Automated Deployment (CI/CD)**

#### **1. GitHub Actions Workflow**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - run: npm test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /var/www/laundrysense
            git pull origin main
            npm ci --only=production
            npm run build
            pm2 restart all
```

#### **2. Docker Deployment**
```bash
# Build and deploy with Docker
docker-compose down
docker-compose build
docker-compose up -d

# Health check
docker-compose ps
curl -f http://localhost/api/health
```

---

## 🔍 **MONITORING & HEALTH CHECKS**

### **Health Check Endpoint**
```typescript
// src/app/api/health/route.ts
export async function GET() {
  const checks = {
    database: await checkDatabase(),
    websocket: await checkWebSocket(),
    memory: process.memoryUsage(),
    uptime: process.uptime()
  };

  const healthy = checks.database && checks.websocket;

  return NextResponse.json({
    status: healthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks
  }, {
    status: healthy ? 200 : 503
  });
}
```

### **Monitoring Setup**
```bash
# Install monitoring tools
npm install @sentry/nextjs
npm install pino pino-pretty

# Setup log rotation
sudo apt install logrotate
```

### **Log Management**
```javascript
// logger.js
import pino from 'pino';

export const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true
    }
  }
});
```

---

## 🔐 **SECURITY CONSIDERATIONS**

### **Environment Security**
```bash
# Secure file permissions
chmod 600 .env
chown root:root .env

# Firewall configuration
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### **SSL/TLS Setup**
```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **Database Security**
```sql
-- Create dedicated database user
CREATE USER 'laundrysense'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON laundrysense.* TO 'laundrysense'@'localhost';
FLUSH PRIVILEGES;
```

---

## 🔄 **BACKUP & RECOVERY**

### **Database Backup**
```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/laundrysense"
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u root -p laundrysense > $BACKUP_DIR/db_$DATE.sql

# Application backup
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /var/www/laundrysense

# Cleanup old backups (keep 7 days)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x backup.sh

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /path/to/backup.sh
```

### **Recovery Process**
```bash
# Database recovery
mysql -u root -p laundrysense < /var/backups/laundrysense/db_YYYYMMDD_HHMMSS.sql

# Application recovery
cd /var/www
tar -xzf /var/backups/laundrysense/app_YYYYMMDD_HHMMSS.tar.gz
pm2 restart all
```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

#### **Database Connection Issues**
```bash
# Check MySQL status
sudo systemctl status mysql

# Check connection
mysql -u username -p -h localhost

# Check logs
sudo tail -f /var/log/mysql/error.log
```

#### **Application Not Starting**
```bash
# Check PM2 logs
pm2 logs

# Check application logs
tail -f logs/application.log

# Check port availability
netstat -tulpn | grep :3000
```

#### **WebSocket Connection Issues**
```bash
# Check WebSocket server
pm2 show laundrysense-websocket

# Test WebSocket connection
wscat -c ws://localhost:3001
```

### **Performance Issues**
```bash
# Check system resources
htop
df -h
free -m

# Check application performance
pm2 monit

# Database performance
mysql -e "SHOW PROCESSLIST;"
```

---

## 📞 **SUPPORT**

### **Emergency Contacts**
- **Development Team**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **On-call Support**: +1-xxx-xxx-xxxx

### **Documentation Links**
- [API Documentation](./API_DOCUMENTATION.md)
- [Development Guide](./HIGH_PRIORITY_IMPLEMENTATION.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)

---

**Document Version**: 1.0  
**Last Updated**: Juny 2025
