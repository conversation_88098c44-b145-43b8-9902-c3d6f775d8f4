@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl lg:text-4xl;
  }

  h2 {
    @apply text-2xl lg:text-3xl;
  }

  h3 {
    @apply text-xl lg:text-2xl;
  }

  h4 {
    @apply text-lg lg:text-xl;
  }

  p {
    @apply leading-7;
  }

  small {
    @apply text-sm font-medium leading-none;
  }
}

@layer components {
  /* Enhanced Form Styles */
  .form-input {
    @apply w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
  }

  .form-input-error {
    @apply border-red-300 bg-red-50 focus:ring-red-500 focus:border-red-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .form-error {
    @apply mt-1 flex items-center gap-1 text-red-600 text-sm;
  }

  /* Button Styles */
  .btn-primary {
    @apply px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium;
  }

  .btn-secondary {
    @apply px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium;
  }

  /* Card Styles */
  .card {
    @apply bg-white rounded-xl border border-gray-200 p-6 shadow-sm;
  }

  .card-header {
    @apply flex items-center gap-2 mb-4;
  }

  /* Mobile Responsive Utilities */
  .mobile-stack {
    @apply flex flex-col gap-3;
  }

  .mobile-stack-sm {
    @apply sm:flex-row sm:gap-4;
  }

  .mobile-grid {
    @apply grid grid-cols-1 gap-4;
  }

  .mobile-grid-sm {
    @apply sm:grid-cols-2;
  }

  .mobile-grid-md {
    @apply md:grid-cols-2;
  }

  .mobile-grid-lg {
    @apply lg:grid-cols-3;
  }

  /* Text Responsive */
  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  /* Spacing Responsive */
  .space-responsive {
    @apply space-y-4 sm:space-y-6;
  }

  .p-responsive {
    @apply p-4 sm:p-6;
  }

  .px-responsive {
    @apply px-4 sm:px-6;
  }

  .py-responsive {
    @apply py-4 sm:py-6;
  }
}

/* Ensure this comes after Tailwind directives if you're overriding or adding utilities */

:root {
  --background: #ffffff;
  --foreground: #171717;
}



@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  /* font-family will be handled by Geist font variables in layout.tsx */
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
  }
}
