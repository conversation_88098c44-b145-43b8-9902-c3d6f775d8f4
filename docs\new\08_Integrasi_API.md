# 8. Integrasi API

Dokumen ini merangkum fitur-fitur utama aplikasi `laundrysense` dan memetakannya ke endpoint API yang relevan, komponen frontend yang menggunakannya, serta data yang dipertukarkan. Ini berfungsi sebagai panduan cepat untuk memahami bagaimana frontend dan backend berkomunikasi.

## 8.1. Tabel Pemetaan Fitur ke API

Tabel berikut merinci integrasi untuk setiap fungsionalitas utama.

| Fitur / Fungsionalitas | Endpoint API | Metode HTTP | Komponen Frontend / Konteks | Deskripsi Data yang Dipertukarkan |
| :--- | :--- | :--- | :--- | :--- |
| **Manajemen Pelanggan** | `/api/customers` | `GET`, `POST` | `manage/customers/page.tsx` | `GET`: Mengambil daftar semua pelanggan. `POST`: Mengirim data pelanggan baru (nama, telepon, alamat). |
| **(Lanjutan)** | `/api/customers/[id]` | `PUT`, `DELETE` | `manage/customers/page.tsx` | `PUT`: Mengirim data pelanggan yang diperbarui. `DELETE`: Tidak ada body, hanya ID di URL. |
| **Manajemen Material** | `/api/materials` | `GET`, `POST` | `manage/materials/page.tsx` | `GET`: Mengambil daftar semua material. `POST`: Mengirim data material baru (nama, stok, unit). |
| **(Lanjutan)** | `/api/materials/[id]` | `PUT`, `DELETE` | `manage/materials/page.tsx` | `PUT`: Mengirim data material yang diperbarui. `DELETE`: Tidak ada body. |
| **Manajemen Transaksi** | `/api/transactions` | `GET`, `POST` | `manage/transactions/page.tsx` | `GET`: Mengambil daftar semua transaksi. `POST`: Mengirim data transaksi baru (ID pelanggan, material, jumlah). |
| **(Lanjutan)** | `/api/transactions/[id]` | `PUT`, `DELETE` | `manage/transactions/page.tsx` | `PUT`: Mengirim status transaksi yang diperbarui. `DELETE`: Tidak ada body. |
| **Peringatan Stok Menipis** | `/api/inventory/low-stock` | `GET` | `DashboardContext.tsx` | `GET`: Mengambil daftar material yang stoknya di bawah ambang batas. Respons berisi `[{ id, name, stock }]`. |
| **Peringatan Cucian Lewat Batas** | `/api/transactions/overdue-pickups` | `GET` | `DashboardContext.tsx` | `GET`: Mengambil daftar transaksi yang sudah selesai tetapi belum diambil melebihi N hari. |
| **Wawasan Dasbor** | `/api/analytics` | `GET` | `DashboardContext.tsx` | `GET`: Endpoint utama untuk `ContextualEngine`. Mengembalikan daftar objek `Insight` yang diprioritaskan. |
| **Pembaruan Real-time** | `ws://localhost:8080` | `WebSocket` | `useWebSocket.ts`, `DashboardContext.tsx` | Menerima pesan siaran (broadcast) seperti `{ type: 'NEW_TRANSACTION', payload: {...} }` saat ada data baru. |
| **Ambil Pola Pelanggan** | `/api/patterns/customers` | `GET` | `analytics/page.tsx` | `GET`: Mengambil data agregat dari tabel `CustomerPattern` untuk analisis lebih lanjut. |
| **Ambil Tren Pendapatan** | `/api/patterns/revenue` | `GET` | `analytics/page.tsx` | `GET`: Mengambil data dari tabel `RevenueTrend` untuk ditampilkan dalam bentuk grafik. |
| **Picu Ulang Analisis** | `/api/patterns/customers` | `POST` | (Untuk Debugging/Admin) `scripts/` | `POST`: Memaksa `PatternAnalysisService` untuk berjalan di luar jadwal. Berguna untuk pengujian. |

## 8.2. Alur Integrasi Khas: Menampilkan Wawasan Dasbor

Untuk memberikan gambaran yang lebih jelas, berikut adalah alur langkah demi langkah bagaimana fitur "Dasbor Kontekstual" diintegrasikan:

1.  **Inisialisasi Frontend**: Pengguna membuka halaman `/dashboard`. Komponen `DashboardProvider` di-mount.
2.  **Koneksi WebSocket**: Di dalam `DashboardContext`, hook `useWebSocket` dipanggil untuk membuat koneksi persisten ke `ws://localhost:8080`.
3.  **Permintaan Wawasan Awal**: `DashboardContext` memanggil hook `useApi` untuk membuat permintaan `GET` ke endpoint `/api/analytics`.
4.  **Pemrosesan Backend**:
    *   Endpoint `/api/analytics` menerima permintaan.
    *   Ia mengaktifkan `ContextualEngine`.
    *   `ContextDetector` berjalan, menganalisis waktu, data historis dari tabel `CustomerPattern`, dan data real-time seperti stok dari tabel `Material`.
    *   `SmartInsightGenerator` mencocokkan konteks dengan `InsightTemplates`.
    *   Hasilnya, sebuah array dari objek `Insight` yang telah diprioritaskan, dibuat.
5.  **Respons API**: Backend mengirimkan array `Insight` ini kembali ke frontend sebagai respons JSON.
6.  **Pembaruan State Frontend**: `DashboardContext` menerima data dan menyimpannya dalam state-nya.
7.  **Render Komponen**: Komponen `ProgressiveInsightCard` membaca state dari `DashboardContext` dan me-render setiap wawasan sesuai dengan datanya (judul, deskripsi, tindakan).
8.  **Pembaruan Real-time**:
    *   Di tempat lain, seorang karyawan menambahkan transaksi baru melalui `/api/transactions`.
    *   Setelah berhasil menyimpan ke DB, backend mengirim pesan `{ type: 'NEW_TRANSACTION' }` melalui WebSocket.
    *   `useWebSocket` di frontend menerima pesan ini.
    *   `DashboardContext` mendeteksi perubahan ini dan secara otomatis memicu permintaan `GET` baru ke `/api/analytics` untuk menyegarkan wawasan dengan data terbaru. Siklus kembali ke langkah 3.