"use client";

import React, { useState } from 'react';

export default function TestApiPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApi = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🚀 Testing API...');
      const response = await fetch('/api/analytics?range=30d');
      console.log('📡 Response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('📊 Raw data:', result);
      
      setData(result);
    } catch (err) {
      console.error('❌ Error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🧪 API Test Page</h1>
      
      <button
        onClick={testApi}
        disabled={loading}
        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mb-6 disabled:opacity-50"
      >
        {loading ? 'Testing...' : 'Test Analytics API'}
      </button>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="font-bold text-red-800 mb-2">❌ Error</h3>
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {data && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-bold text-green-800 mb-4">✅ API Response</h3>
          <div className="space-y-2 text-sm">
            <p><strong>Total Revenue:</strong> Rp {data.totalRevenue?.toLocaleString('id-ID') || '0'}</p>
            <p><strong>Total Transactions:</strong> {data.totalTransactions || '0'}</p>
            <p><strong>Average Transaction Value:</strong> Rp {data.averageTransactionValue?.toLocaleString('id-ID') || '0'}</p>
            <p><strong>Active Customers:</strong> {data.activeCustomers || '0'}</p>
          </div>
          
          <details className="mt-4">
            <summary className="cursor-pointer font-semibold text-green-800">Raw JSON Data</summary>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
              {JSON.stringify(data, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
}
