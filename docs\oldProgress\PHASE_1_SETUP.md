# LaundrySense - Phase 1: Foundation & Core Engine

## Overview
Phase 1 establishes the foundational backend infrastructure for LaundrySense, including database schema, API endpoints, and basic pattern detection capabilities.

## 🏗️ Architecture

### Database Schema
- **Customers**: Customer information with behavioral intelligence scores
- **MaterialInventory**: Laundry materials with stock tracking
- **Transactions**: Service transactions with contextual data
- **TransactionMaterials**: Junction table for materials used per transaction

### API Endpoints
- `/api/customers` - Customer management CRUD
- `/api/materials` - Material inventory CRUD
- `/api/transactions` - Transaction management CRUD

## 🚀 Setup Instructions

### Prerequisites
- Node.js 18+
- MySQL 8.0+
- npm or yarn

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your database credentials (without password)
DATABASE_URL="mysql://root@localhost:3306/laundrysense"
```

### 2. Install Dependencies
```bash
# Install all dependencies
npm install

# Or with yarn
yarn install
```

### 3. Database Setup
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (for development)
npm run db:push

# Or run migrations (for production)
npm run db:migrate

# Seed database with sample data
npm run db:seed
```

### 4. Development Server
```bash
# Start development server
npm run dev

# Server will be available at http://localhost:3000
```

## 📊 Database Schema Details

### Customers Table
```sql
- id: String (CUID)
- name: String
- phone_number: String (unique)
- email: String (optional, unique)
- address: String (optional)
- registration_date: DateTime
- last_transaction_date: DateTime (optional)
- behavior_frequency_score: Float (0.0-1.0)
- behavior_preference_score: Float (0.0-1.0)
- behavior_seasonal_bias: Float (0.0-1.0)
```

### MaterialInventory Table
```sql
- id: String (CUID)
- material_name: String (unique)
- current_stock_unit: Float
- unit_of_measure: String
- last_restock_date: DateTime
- usage_rate_per_transaction: Float
- usage_rate_per_kg: Float
- minimum_stock_threshold: Float
- cost_per_unit: Float
- supplier_info: String (optional)
- category: MaterialCategory (enum)
```

### Transactions Table
```sql
- id: String (CUID)
- customer_id: String (FK)
- service_type: ServiceType (enum)
- weight_kg: Float
- price: Float
- transaction_date: DateTime
- status: TransactionStatus (enum)
- context_weather: WeatherContext (enum)
- context_day_type: DayType (enum)
- context_seasonal_factor: Float (0.0-1.0)
- context_time_of_day: TimeOfDay (enum)
- pickup_date: DateTime (optional)
- delivery_date: DateTime (optional)
- special_instructions: String (optional)
- discount_applied: Float
```

## 🔧 API Usage Examples

### Create Customer
```bash
curl -X POST http://localhost:3000/api/customers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "phone_number": "081234567890",
    "email": "<EMAIL>",
    "address": "Jl. Example No. 123"
  }'
```

### Get Customers (Paginated)
```bash
curl "http://localhost:3000/api/customers?page=1&limit=10&search=John"
```

### Create Material
```bash
curl -X POST http://localhost:3000/api/materials \
  -H "Content-Type: application/json" \
  -d '{
    "material_name": "Premium Detergent",
    "current_stock_unit": 100.0,
    "unit_of_measure": "liter",
    "category": "DETERGENT",
    "cost_per_unit": 15000
  }'
```

### Create Transaction
```bash
curl -X POST http://localhost:3000/api/transactions \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_id_here",
    "service_type": "CUCI_SETRIKA",
    "weight_kg": 5.0,
    "price": 50000,
    "context_weather": "SUNNY",
    "context_day_type": "WEEKDAY"
  }'
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Database Setup
For testing, create a separate test database:
```sql
CREATE DATABASE laundrysense_test;
```

Update your `.env` file:
```
DATABASE_URL_TEST="mysql://root@localhost:3306/laundrysense_test"
```

## 📈 Performance Optimizations

### Database Indexes
The schema includes optimized indexes for:
- Customer phone/email lookups
- Transaction date ranges
- Material category filtering
- Contextual data queries

### API Features
- Pagination for large datasets
- Search functionality
- Filtering by multiple criteria
- Proper error handling
- Input validation with Zod

## 🔍 Contextual Intelligence Features

### Behavioral Scoring
- **Frequency Score**: How often customer uses service
- **Preference Score**: Service type preferences
- **Seasonal Bias**: Usage patterns across seasons

### Transaction Context
- **Weather Context**: Weather conditions during transaction
- **Day Type**: Weekday/weekend/holiday classification
- **Time of Day**: Morning/afternoon/evening patterns
- **Seasonal Factor**: Peak/low season indicator

## 🚨 Error Handling

All API endpoints include comprehensive error handling:
- Input validation errors (400)
- Resource not found errors (404)
- Server errors (500)
- Detailed error messages for debugging

## 📝 Next Steps

Phase 1 provides the foundation for:
- Phase 2: Contextual Intelligence Layer
- Phase 3: Progressive UI Development
- Phase 4: Production Optimization

The database schema and API structure are designed to support advanced pattern detection and contextual insights in subsequent phases.
