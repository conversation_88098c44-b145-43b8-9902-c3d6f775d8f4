# Dokumentasi Fase 3: UI Progresif & Integrasi Real-time

**Fase 3** menandai transisi dari backend yang kuat ke antarmuka pengguna (UI) yang fungsional dan cerdas. Fase ini dibagi menjadi dua bagian utama:

1.  **Fase 3A**: Membangun UI dasbor yang progresif dan sadar konteks.
2.  **Fase 3B**: Mengganti simulasi data manual dengan integrasi backend real-time menggunakan WebSockets.

---

## Fase 3A: Pengembangan UI Progresif

### Tujuan

Tujuan dari Fase 3A adalah untuk menciptakan dasbor yang tidak hanya menampilkan data, tetapi juga menyajikannya dengan cara yang cerdas. Konsep utamanya adalah **pengungkapan progresif** (progressive disclosure), di mana pengguna pertama-tama melihat ringkasan tingkat tinggi dan kemudian dapat menelusuri detail sesuai kebutuhan. Ini mencegah pengguna merasa kewalahan oleh informasi.

### Komponen Arsitektur Utama

Arsitektur frontend dibangun di sekitar beberapa komponen kunci untuk memastikan skalabilitas dan kemudahan pemeliharaan:

1.  **`DashboardProvider` (`app/context/DashboardContext.tsx`)**
    *   Berfungsi sebagai sumber kebenaran tunggal (single source of truth) untuk semua state terkait dasbor.
    *   Menggunakan React Context API untuk menyediakan data konteks ke seluruh pohon komponen tanpa perlu *prop drilling*.

2.  **Layout Utama (`app/components/layout/MainLayout.tsx`)**
    *   Menyediakan struktur halaman yang konsisten, termasuk header, sidebar, dan area konten utama.
    *   Memastikan pengalaman pengguna yang seragam di seluruh aplikasi.

3.  **Komponen Kartu (Card Components)**
    *   **`StatCard`**: Kartu serbaguna untuk menampilkan metrik kunci (misalnya, Konteks Saat Ini, Mode Dasbor yang Direkomendasikan).
    *   **`InsightCard`**: Kartu yang dirancang khusus untuk menampilkan *priority insights* dengan ikon, judul, deskripsi, dan tindakan yang disarankan.
    *   **`ModeCard`**: Kartu yang menampilkan mode dasbor yang direkomendasikan, membantu pengguna fokus pada tugas yang paling relevan.

4.  **Utilitas dan Helper (`app/lib/utils.ts`, `app/lib/icon-map.tsx`)**
    *   Fungsi pembantu untuk memformat tanggal, angka, dan string.
    *   Mekanisme pemetaan untuk secara dinamis menampilkan ikon `lucide-react` berdasarkan string konteks (misalnya, 'Pagi' -> ikon Matahari).

### Filosofi Desain

*   **Sadar Konteks**: UI secara visual beradaptasi berdasarkan `currentContextObject`. Misalnya, menampilkan ikon yang berbeda atau menyorot metrik yang berbeda.
*   **Berbasis Komponen**: Memecah UI menjadi komponen-komponen kecil yang dapat digunakan kembali, membuatnya lebih mudah untuk dikelola dan diuji.
*   **Desain Minimalis**: Fokus pada kejelasan dan kemudahan penggunaan, menghindari kekacauan visual.

---

## Fase 3B: Integrasi Backend Real-time

### Tujuan

Fase 3B bertujuan untuk menghidupkan dasbor dengan data real-time. Ini melibatkan penggantian semua logika simulasi manual di frontend dengan koneksi langsung ke backend melalui WebSockets.

### Detail Implementasi Kunci

1.  **Server WebSocket Kustom (`server.ts`)**
    *   Sebuah server Node.js yang berjalan bersama server Next.js.
    *   Menggunakan paket `ws` untuk membuat `WebSocketServer` yang menangani koneksi klien.
    *   Secara berkala (setiap 15 detik), server ini:
        1.  Memanggil `generateRandomContextUpdate()` untuk mensimulasikan deteksi konteks baru di backend.
        2.  Menyiarkan (`broadcast`) hasil deteksi konteks ke semua klien yang terhubung.

2.  **Hook `useWebSocket` (`app/hooks/useWebSocket.ts`)**
    *   Hook React kustom yang merangkum semua logika koneksi WebSocket di sisi klien.
    *   Fitur utama:
        *   Secara otomatis terhubung ke endpoint WebSocket (`ws://localhost:3000`).
        *   Mendengarkan pesan masuk dan memanggil fungsi `onMessage` yang disediakan.
        *   Menerapkan **logika koneksi ulang otomatis** dengan penundaan eksponensial jika koneksi terputus.
        *   Menyediakan status koneksi (Connecting, Open, Closed).

3.  **Pembaruan `DashboardProvider`**
    *   `DashboardProvider` diintegrasikan dengan hook `useWebSocket`.
    *   Ketika pesan `CONTEXT_UPDATE` diterima dari server, provider memperbarui state-nya, yang secara otomatis memicu render ulang di seluruh aplikasi dengan data baru.

4.  **Pemetaan Insight (`lib/contextual-intelligence/insight-mapper.ts`)**
    *   Sebuah utilitas penting yang menjembatani kesenjangan antara data backend dan frontend.
    *   Fungsi `mapInsights` mengubah array string `PriorityInsight` dari backend menjadi array objek `Insight` yang kaya, yang dibutuhkan oleh `InsightCard` di frontend.

### Pergeseran Arsitektur

Fase ini menandai pergeseran penting:

*   **Dari Simulasi Klien ke Penggerak Backend**: State dasbor tidak lagi disimulasikan di browser. Sekarang, ia sepenuhnya didorong oleh pembaruan yang dikirim oleh backend.
*   **Pemisahan Tanggung Jawab**: Logika pembuatan data (simulasi) sekarang berada di backend (`server.ts`), sementara frontend hanya bertanggung jawab untuk menampilkan data yang diterimanya.

### Tantangan Teknis dan Solusi

*   **Ketidakcocokan Tipe Data**: Awalnya, ada perbedaan antara tipe data `ContextDetectionResult` di backend dan `DashboardState` di frontend. Ini diselesaikan dengan:
    *   Menstandardisasi nama tipe menjadi `DashboardState`.
    *   Membuat pemetaan eksplisit (`insight-mapper`) untuk memastikan data sesuai dengan yang diharapkan oleh UI.
*   **Struktur Payload WebSocket**: Memastikan struktur objek JSON yang dikirim oleh server (`{ type: 'CONTEXT_UPDATE', payload: ... }`) sama persis dengan yang diharapkan oleh `DashboardProvider` di frontend.
*   **Manajemen Dependensi**: Menghapus paket `websocket` yang tidak perlu untuk menghindari konflik dengan paket `ws` yang sudah digunakan, menjaga proyek tetap bersih.

---

### Hasil Akhir Fase 3

Pada akhir Fase 3, LaundrySense memiliki dasbor yang dinamis dan berfungsi penuh yang diperbarui secara real-time. Aplikasi ini sekarang:

*   **Otomatis**: Tidak memerlukan intervensi manual untuk menyegarkan data konteks.
*   **Responsif**: UI bereaksi secara instan terhadap perubahan yang dikirim dari backend.
*   **Scalable**: Arsitektur yang ada (konteks, hook, komponen) siap untuk mendukung fitur-fitur masa depan seperti notifikasi, pelaporan, dan gamifikasi.
