"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTransactions, useCustomersList, useMaterialsList } from '@/hooks/useApi';
import DataTable from '@/components/ui/DataTable';
import Modal from '@/components/ui/Modal';
import Form<PERSON>ield from '@/components/ui/FormField';
import EnhancedTransactionForm from '@/components/forms/EnhancedTransactionForm';
import { Receipt, User, Calendar, DollarSign, Weight } from 'lucide-react';

interface Transaction {
  id: string;
  customer_id: string;
  service_type: string;
  weight_kg: number;
  price: number;
  transaction_date: string;
  status: string;
  context_weather: string;
  context_day_type: string;
  context_time_of_day: string;
  pickup_date?: string;
  delivery_date?: string;
  special_instructions?: string;
  discount_applied: number;
  customer: {
    name: string;
    phone_number: string;
  };
}

interface TransactionFormData {
  customer_id: string;
  service_type: string;
  weight_kg: number;
  price: number;
  status: string;
  context_weather: string;
  context_day_type: string;
  context_time_of_day: string;
  pickup_date: string;
  delivery_date: string;
  special_instructions: string;
  discount_applied: number;
  materials: Array<{
    material_id: string;
    quantity_used: number;
  }>;
}

interface TransactionFormErrors {
  customer_id?: string;
  service_type?: string;
  weight_kg?: string;
  price?: string;
  status?: string;
  context_weather?: string;
  context_day_type?: string;
  context_time_of_day?: string;
  pickup_date?: string;
  delivery_date?: string;
  special_instructions?: string;
  discount_applied?: string;
}

const SERVICE_TYPES = [
  { value: 'CUCI_KERING', label: 'Cuci & Kering' },
  { value: 'CUCI_SETRIKA', label: 'Cuci & Setrika' },
  { value: 'SETRIKA_SAJA', label: 'Setrika Saja' },
  { value: 'CUCI_SAJA', label: 'Cuci Saja' },
  { value: 'DRY_CLEAN', label: 'Dry Clean' },
  { value: 'SEPATU', label: 'Cuci Sepatu' },
  { value: 'KARPET', label: 'Cuci Karpet' },
  { value: 'SELIMUT', label: 'Cuci Selimut' },
];

const TRANSACTION_STATUS = [
  { value: 'PENDING', label: 'Menunggu' },
  { value: 'IN_PROGRESS', label: 'Sedang Diproses' },
  { value: 'READY', label: 'Siap Diambil' },
  { value: 'COMPLETED', label: 'Selesai' },
  { value: 'CANCELLED', label: 'Dibatalkan' },
];

const WEATHER_CONTEXT = [
  { value: 'SUNNY', label: 'Cerah' },
  { value: 'RAINY', label: 'Hujan' },
  { value: 'CLOUDY', label: 'Berawan' },
  { value: 'STORMY', label: 'Badai' },
  { value: 'HOT', label: 'Panas' },
  { value: 'HUMID', label: 'Lembab' },
];

const DAY_TYPE = [
  { value: 'WEEKDAY', label: 'Hari Kerja' },
  { value: 'WEEKEND', label: 'Akhir Pekan' },
  { value: 'HOLIDAY', label: 'Hari Libur' },
  { value: 'SPECIAL_EVENT', label: 'Acara Khusus' },
];

const TIME_OF_DAY = [
  { value: 'EARLY_MORNING', label: 'Pagi Dini (6-9)' },
  { value: 'MORNING', label: 'Pagi (9-12)' },
  { value: 'AFTERNOON', label: 'Siang (12-17)' },
  { value: 'EVENING', label: 'Sore (17-20)' },
  { value: 'NIGHT', label: 'Malam (20-22)' },
];

const TransactionsPage: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [materials, setMaterials] = useState<any[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null);
  const [useEnhancedForm, setUseEnhancedForm] = useState(true);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('transaction_date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const [formData, setFormData] = useState<TransactionFormData>({
    customer_id: '',
    service_type: '',
    weight_kg: 0,
    price: 0,
    status: 'PENDING',
    context_weather: 'SUNNY',
    context_day_type: 'WEEKDAY',
    context_time_of_day: 'MORNING',
    pickup_date: '',
    delivery_date: '',
    special_instructions: '',
    discount_applied: 0,
    materials: [],
  });
  const [formErrors, setFormErrors] = useState<TransactionFormErrors>({});

  const {
    loading: transactionsLoading,
    error: transactionsError,
    getTransactions,
    createTransaction,
    updateTransaction,
    deleteTransaction,
  } = useTransactions();

  const { getCustomersList } = useCustomersList();
  const { getMaterialsList } = useMaterialsList();

  const [isLoading, setIsLoading] = useState(false);

  const loadTransactions = useCallback(async () => {
    if (isLoading) return; // Prevent multiple simultaneous calls

    setIsLoading(true);
    try {
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm,
        sortBy,
        sortOrder,
      };

      const response = await getTransactions(params);

      if (response.success && response.data) {
        setTransactions(response.data.transactions || response.data);
        setTotalTransactions(response.data.total || response.data.length || 0);
      } else {
        setTransactions([]);
        setTotalTransactions(0);
      }
    } catch (error) {
      console.error('Error loading transactions:', error);
      setTransactions([]);
      setTotalTransactions(0);
    } finally {
      setIsLoading(false);
    }
  }, [getTransactions, currentPage, itemsPerPage, searchTerm, sortBy, sortOrder, isLoading]);

  const loadCustomers = useCallback(async () => {
    const response = await getCustomersList();
    if (response.success && response.data) {
      // Fix: response.data is an object with customers array, not array directly
      if (Array.isArray(response.data)) {
        setCustomers(response.data);
      } else if (response.data.customers && Array.isArray(response.data.customers)) {
        setCustomers(response.data.customers);
      } else {
        setCustomers([]);
      }
    }
  }, [getCustomersList]);

  const loadMaterials = useCallback(async () => {
    const response = await getMaterialsList();
    if (response.success && response.data) {
      // Fix: response.data might be an object with materials array, not array directly
      if (Array.isArray(response.data)) {
        setMaterials(response.data);
      } else if (response.data.materials && Array.isArray(response.data.materials)) {
        setMaterials(response.data.materials);
      } else {
        setMaterials([]);
      }
    }
  }, [getMaterialsList]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadTransactions();
    }, 100); // Debounce to prevent rapid calls

    return () => clearTimeout(timeoutId);
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortOrder]); // Remove loadTransactions from dependencies

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadTransactions(); // FIXED: Add loadTransactions on mount
      loadCustomers();
      loadMaterials();
    }, 100); // Debounce to prevent rapid calls

    return () => clearTimeout(timeoutId);
  }, []); // Empty dependency array - only run once on mount

  const validateForm = (): boolean => {
    const errors: TransactionFormErrors = {};

    if (!formData.customer_id) {
      errors.customer_id = 'Pelanggan wajib dipilih';
    }

    if (!formData.service_type) {
      errors.service_type = 'Jenis layanan wajib dipilih';
    }

    if (formData.weight_kg <= 0) {
      errors.weight_kg = 'Berat harus lebih dari 0';
    }

    if (formData.price < 0) {
      errors.price = 'Harga tidak boleh negatif';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Fix data types for API validation
    const submitData = {
      ...formData,
      customer_id: String(formData.customer_id), // Convert to string
      pickup_date: formData.pickup_date ? new Date(formData.pickup_date).toISOString() : null, // Convert to ISO datetime
      delivery_date: formData.delivery_date ? new Date(formData.delivery_date).toISOString() : null, // Convert to ISO datetime
      special_instructions: formData.special_instructions || null,
      materials: formData.materials?.map(material => ({
        ...material,
        material_id: String(material.material_id) // Convert to string
      })) || []
    };

    let response;
    if (editingTransaction) {
      response = await updateTransaction(editingTransaction.id, submitData);
    } else {
      response = await createTransaction(submitData);
    }

    if (response.success) {
      setIsModalOpen(false);
      resetForm();
      loadTransactions();
    }
  };

  const handleEdit = (transaction: Transaction) => {
    setEditingTransaction(transaction);
    setFormData({
      customer_id: transaction.customer_id,
      service_type: transaction.service_type,
      weight_kg: transaction.weight_kg,
      price: transaction.price,
      status: transaction.status,
      context_weather: transaction.context_weather,
      context_day_type: transaction.context_day_type,
      context_time_of_day: transaction.context_time_of_day,
      pickup_date: transaction.pickup_date ? transaction.pickup_date.split('T')[0] : '',
      delivery_date: transaction.delivery_date ? transaction.delivery_date.split('T')[0] : '',
      special_instructions: transaction.special_instructions || '',
      discount_applied: transaction.discount_applied,
      materials: [],
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (transaction: Transaction) => {
    if (window.confirm(`Apakah Anda yakin ingin menghapus transaksi ini?`)) {
      const response = await deleteTransaction(transaction.id);
      if (response.success) {
        loadTransactions();
      }
    }
  };

  const resetForm = () => {
    setFormData({
      customer_id: '',
      service_type: '',
      weight_kg: 0,
      price: 0,
      status: 'PENDING',
      context_weather: 'SUNNY',
      context_day_type: 'WEEKDAY',
      context_time_of_day: 'MORNING',
      pickup_date: '',
      delivery_date: '',
      special_instructions: '',
      discount_applied: 0,
      materials: [],
    });
    setFormErrors({});
    setEditingTransaction(null);
  };

  const handleCreate = () => {
    resetForm();
    setIsModalOpen(true);
  };

  const getServiceTypeLabel = (serviceType: string) => {
    const service = SERVICE_TYPES.find(s => s.value === serviceType);
    return service ? service.label : serviceType;
  };

  const getStatusLabel = (status: string) => {
    const statusObj = TRANSACTION_STATUS.find(s => s.value === status);
    return statusObj ? statusObj.label : status;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'text-yellow-600 bg-yellow-100';
      case 'IN_PROGRESS': return 'text-blue-600 bg-blue-100';
      case 'READY': return 'text-green-600 bg-green-100';
      case 'COMPLETED': return 'text-gray-600 bg-gray-100';
      case 'CANCELLED': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Safe customer options with additional validation
  const customerOptions = Array.isArray(customers)
    ? customers.map((customer: any) => ({
        value: customer.id,
        label: `${customer.name} (${customer.phone_number})`,
      }))
    : [];



  const columns = [
    {
      key: 'customer',
      label: 'Pelanggan',
      render: (value: any) => (
        <div className="flex items-center gap-2">
          <User size={16} className="text-gray-400" />
          <div>
            <div className="font-medium">{value.name}</div>
            <div className="text-sm text-gray-500">{value.phone_number}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'service_type',
      label: 'Layanan',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Receipt size={16} className="text-gray-400" />
          <span>{getServiceTypeLabel(value)}</span>
        </div>
      ),
    },
    {
      key: 'weight_kg',
      label: 'Berat',
      render: (value: number) => (
        <div className="flex items-center gap-1">
          <Weight size={14} className="text-gray-400" />
          <span>{value} kg</span>
        </div>
      ),
    },
    {
      key: 'price',
      label: 'Harga',
      render: (value: number, row: Transaction) => (
        <div>
          <div className="flex items-center gap-1">
            <DollarSign size={14} className="text-green-500" />
            <span className="font-medium">Rp {value.toLocaleString('id-ID')}</span>
          </div>
          {row.discount_applied > 0 && (
            <div className="text-xs text-red-500">
              Diskon: Rp {row.discount_applied.toLocaleString('id-ID')}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(value)}`}>
          {getStatusLabel(value)}
        </span>
      ),
    },
    {
      key: 'transaction_date',
      label: 'Tanggal',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Calendar size={16} className="text-gray-400" />
          <span className="text-sm">{new Date(value).toLocaleDateString('id-ID')}</span>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manajemen Transaksi</h1>
          <div className="mt-2 flex items-center gap-4">
            <label className="flex items-center gap-2 text-sm text-gray-600">
              <input
                type="checkbox"
                checked={useEnhancedForm}
                onChange={(e) => setUseEnhancedForm(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              Gunakan Form Enhanced (Recommended)
            </label>
          </div>
        </div>
        <button
          onClick={handleCreate}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
        >
          <Receipt size={20} />
          Tambah Transaksi
        </button>
      </div>

      {transactionsError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {transactionsError}
        </div>
      )}

      <DataTable
        title="Daftar Transaksi"
        columns={columns}
        data={transactions}
        loading={transactionsLoading || isLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onCreate={handleCreate}
        emptyMessage="Belum ada transaksi"
        totalItems={totalTransactions}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        onPageChange={setCurrentPage}
        onItemsPerPageChange={(size) => {
          setItemsPerPage(size);
          setCurrentPage(1);
        }}
        onSort={(key, order) => {
          setSortBy(key);
          setSortOrder(order);
        }}
        onSearch={setSearchTerm}
        itemsPerPageOptions={[15, 25, 50]}
      />

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingTransaction ? 'Edit Transaksi' : 'Tambah Transaksi'}
        size="full"
      >
        {useEnhancedForm ? (
          <EnhancedTransactionForm
            initialData={editingTransaction ? {
              customer_id: editingTransaction.customer_id,
              service_type: editingTransaction.service_type,
              weight_kg: editingTransaction.weight_kg,
              price: editingTransaction.price,
              status: editingTransaction.status,
              context_weather: editingTransaction.context_weather,
              context_day_type: editingTransaction.context_day_type,
              context_time_of_day: editingTransaction.context_time_of_day,
              pickup_date: editingTransaction.pickup_date ? editingTransaction.pickup_date.split('T')[0] : '',
              delivery_date: editingTransaction.delivery_date ? editingTransaction.delivery_date.split('T')[0] : '',
              special_instructions: editingTransaction.special_instructions || '',
              discount_applied: editingTransaction.discount_applied,
              materials: [],
            } : undefined}
            customers={customers}
            materials={materials}
            onSubmit={async (data) => {
              const submitData = {
                ...data,
                customer_id: String(data.customer_id),
                pickup_date: data.pickup_date ? new Date(data.pickup_date).toISOString() : null,
                delivery_date: data.delivery_date ? new Date(data.delivery_date).toISOString() : null,
                special_instructions: data.special_instructions || null,
                materials: data.materials?.map(material => ({
                  ...material,
                  material_id: String(material.material_id)
                })) || []
              };

              const response = editingTransaction
                ? await updateTransaction(editingTransaction.id, submitData)
                : await createTransaction(submitData);

              if (response.success) {
                setIsModalOpen(false);
                resetForm();
                loadTransactions();
              }
            }}
            onCancel={() => setIsModalOpen(false)}
            isLoading={transactionsLoading}
            isEditing={!!editingTransaction}
          />
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Informasi Dasar</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Pelanggan"
                name="customer_id"
                type="select"
                value={formData.customer_id}
                onChange={(value) => setFormData({ ...formData, customer_id: value as string })}
                error={formErrors.customer_id}
                options={customerOptions}
                required
              />

              <FormField
                label="Jenis Layanan"
                name="service_type"
                type="select"
                value={formData.service_type}
                onChange={(value) => setFormData({ ...formData, service_type: value as string })}
                error={formErrors.service_type}
                options={SERVICE_TYPES}
                required
              />

              <FormField
                label="Berat (kg)"
                name="weight_kg"
                type="number"
                value={formData.weight_kg}
                onChange={(value) => setFormData({ ...formData, weight_kg: value as number })}
                error={formErrors.weight_kg}
                min={0}
                step={0.1}
                required
              />

              <FormField
                label="Harga"
                name="price"
                type="number"
                value={formData.price}
                onChange={(value) => setFormData({ ...formData, price: value as number })}
                error={formErrors.price}
                min={0}
                step={0.01}
                required
              />

              <FormField
                label="Status"
                name="status"
                type="select"
                value={formData.status}
                onChange={(value) => setFormData({ ...formData, status: value as string })}
                options={TRANSACTION_STATUS}
              />

              <FormField
                label="Diskon"
                name="discount_applied"
                type="number"
                value={formData.discount_applied}
                onChange={(value) => setFormData({ ...formData, discount_applied: value as number })}
                min={0}
                step={0.01}
              />
            </div>
          </div>

          {/* Context Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Informasi Konteks</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Cuaca"
                name="context_weather"
                type="select"
                value={formData.context_weather}
                onChange={(value) => setFormData({ ...formData, context_weather: value as string })}
                options={WEATHER_CONTEXT}
              />

              <FormField
                label="Jenis Hari"
                name="context_day_type"
                type="select"
                value={formData.context_day_type}
                onChange={(value) => setFormData({ ...formData, context_day_type: value as string })}
                options={DAY_TYPE}
              />

              <FormField
                label="Waktu"
                name="context_time_of_day"
                type="select"
                value={formData.context_time_of_day}
                onChange={(value) => setFormData({ ...formData, context_time_of_day: value as string })}
                options={TIME_OF_DAY}
              />

            </div>
          </div>

          {/* Schedule Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Jadwal</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label="Tanggal Pickup"
                name="pickup_date"
                type="text"
                value={formData.pickup_date}
                onChange={(value) => setFormData({ ...formData, pickup_date: value as string })}
                placeholder="YYYY-MM-DD"
              />

              <FormField
                label="Tanggal Delivery"
                name="delivery_date"
                type="text"
                value={formData.delivery_date}
                onChange={(value) => setFormData({ ...formData, delivery_date: value as string })}
                placeholder="YYYY-MM-DD"
              />
            </div>

            <FormField
              label="Instruksi Khusus"
              name="special_instructions"
              type="textarea"
              value={formData.special_instructions}
              onChange={(value) => setFormData({ ...formData, special_instructions: value as string })}
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={transactionsLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {transactionsLoading ? 'Menyimpan...' : editingTransaction ? 'Update' : 'Simpan'}
            </button>
          </div>
        </form>
        )}
      </Modal>
    </div>
  );
};

export default TransactionsPage;
