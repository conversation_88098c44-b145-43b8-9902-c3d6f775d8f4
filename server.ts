import { createServer, IncomingMessage, ServerResponse } from 'http';
import { parse } from 'url';
import next from 'next';
import { WebSocketServer, WebSocket } from 'ws';
import { PrismaClient } from '@prisma/client';
import { getRealTimeKpis } from './src/lib/data-access/kpis';
import { getPatternData } from './src/lib/data-access/patterns';
import { PatternCalculationJob } from './src/lib/cron/pattern-calculation-job';

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = 3000;

// Initialize Next.js app and Prisma Client
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();
const prisma = new PrismaClient();

// Initialize Pattern Calculation Job
const patternCalculationJob = new PatternCalculationJob();

app.prepare().then(() => {
  // Create a single HTTP server to handle both Next.js and WebSocket traffic
  const httpServer = createServer((req: IncomingMessage, res: ServerResponse) => {
    try {
      const parsedUrl = parse(req.url!, true);
      handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error handling request:', err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // Mount the WebSocket server on the HTTP server
  const wss = new WebSocketServer({ server: httpServer });

  // Helper function to broadcast data to all connected clients
  const broadcast = (data: object) => {
    const jsonData = JSON.stringify(data);
    wss.clients.forEach((client: WebSocket) => {
      if (client.readyState === WebSocket.OPEN) {
        try {
          client.send(jsonData);
        } catch (error) {
          console.error('[WSS] Error sending data to client:', error);
        }
      }
    });
  };

  // Central function to fetch the latest dashboard state from the database
  const fetchLatestDashboardState = async () => {
    try {
      const [kpis, patterns] = await Promise.all([
        getRealTimeKpis(prisma),
        getPatternData(prisma),
      ]);
      return { kpis, patterns };
    } catch (error) {
      console.error('[WSS] Error fetching latest dashboard state:', error);
      return { kpis: null, patterns: null }; // Return a safe state on error
    }
  };

  // Main WebSocket connection handler
  wss.on('connection', async (ws: WebSocket) => {
    console.log('✅ WebSocket client connected');

    // 1. Send initial state to the newly connected client
    console.log('[WSS] Fetching initial state for new client...');
    try {
      const initialState = await fetchLatestDashboardState();
      console.log('[WSS] Initial state data:', JSON.stringify(initialState, null, 2));

      if (ws.readyState === WebSocket.OPEN) {
        const message = {
          type: 'INITIAL_STATE',
          payload: initialState
        };
        const messageString = JSON.stringify(message);
        console.log('[WSS] Sending message size:', messageString.length, 'bytes');

        ws.send(messageString);
        console.log('[WSS] Successfully sent initial state to client');
      }
    } catch (error) {
      console.error('[WSS] Error sending initial state:', error);
    }

    // 2. Listen for messages from this specific client
    ws.on('message', async (message: Buffer) => {
      try {
        const parsedMessage = JSON.parse(message.toString());
        console.log('[WSS] Received message:', parsedMessage);

        // If a trigger is received, fetch the latest state and broadcast to ALL clients
        if (parsedMessage.type === 'TRIGGER_ANALYSIS') {
          console.log('[WSS] Analysis trigger received. Fetching and broadcasting latest state...');
          const latestState = await fetchLatestDashboardState();
          broadcast({
            type: 'DASHBOARD_STATE_UPDATE',
            payload: latestState,
          });
        }
      } catch (error) {
        console.error('[WSS] Error processing message:', error);
      }
    });

    // 3. Handle client disconnection
    ws.on('close', (code: number, reason: Buffer) => {
      console.log(`❌ WebSocket client disconnected - Code: ${code}, Reason: ${reason.toString()}`);
    });

    // 4. Handle errors
    ws.on('error', (error: Error) => {
      console.error('[WSS] WebSocket error:', error);
    });
  });

  // Start the HTTP server
  httpServer
    .listen(port, () => {
      console.log(`> Next.js server ready on http://${hostname}:${port}`);
      console.log(`> WebSocket server listening on ws://${hostname}:${port}`);

      // Start background pattern calculation jobs
      console.log('🤖 Starting automated pattern calculation jobs...');
      patternCalculationJob.startDailyCalculation();
      patternCalculationJob.startWeeklyCalculation();
      console.log('✅ Background jobs scheduled successfully');

      // Run initial pattern calculation if no patterns exist
      setTimeout(async () => {
        try {
          console.log('🔍 Checking if initial pattern calculation is needed...');
          const existingPatterns = await getPatternData(prisma);

          if (!existingPatterns.customer_patterns && !existingPatterns.material_usage_patterns && !existingPatterns.revenue_patterns) {
            console.log('📊 No existing patterns found. Running initial calculation...');
            await patternCalculationJob.runFullCalculation();
            console.log('✅ Initial pattern calculation completed');
          } else {
            console.log('✅ Existing patterns found. Skipping initial calculation.');
          }
        } catch (error) {
          console.error('❌ Error during initial pattern check:', error);
        }
      }, 5000); // Wait 5 seconds after server start
    })
    .on('error', (err: Error) => {
      console.error('HTTP Server Error:', err);
      process.exit(1);
    });
});