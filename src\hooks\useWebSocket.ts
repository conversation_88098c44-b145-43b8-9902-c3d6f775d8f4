"use client";

import { useEffect, useRef, useState, useCallback } from 'react';

interface WebSocketMessage {
  type: string;
  payload: any; // FIX: Change 'data' to 'payload' to match server
  timestamp: string;
}

interface UseWebSocketOptions {
  url?: string;
  onMessage?: (message: WebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const {
    url = 'ws://localhost:3000', // FIXED: Connect to the correct WebSocket port (same as HTTP server)
    onMessage,
    onConnect,
    onDisconnect,
    onError,
    reconnectInterval = 5000,
    maxReconnectAttempts = 5,
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionStatus('connecting');

    try {
      wsRef.current = new WebSocket(url);

      wsRef.current.onopen = () => {
        setIsConnected(true);
        setConnectionStatus('connected');
        setReconnectAttempts(0);
        if (onConnect) onConnect();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const parsedMessage = JSON.parse(event.data);
          // FIX: Create a message object that conforms to the new interface
          const message: WebSocketMessage = {
            type: parsedMessage.type,
            payload: parsedMessage.payload,
            timestamp: new Date().toISOString()
          };
          setLastMessage(message);
          if (onMessage) onMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        setIsConnected(false);
        setConnectionStatus('disconnected');
        if (onDisconnect) onDisconnect();

        // Attempt to reconnect
        if (reconnectAttempts < maxReconnectAttempts) {
          setReconnectAttempts(prev => prev + 1);
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (error) => {
        setConnectionStatus('error');
        if (onError) onError(error);
      };

    } catch (error) {
      setConnectionStatus('error');
      console.error('Failed to create WebSocket connection:', error);
    }
  }, [url, reconnectInterval, maxReconnectAttempts]); // Removed callback dependencies to prevent infinite loops

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus('disconnected');
    setReconnectAttempts(0);
  }, []);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        const messageToSend = {
          ...message,
          timestamp: new Date().toISOString(),
        };
        wsRef.current.send(JSON.stringify(messageToSend));
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        return false;
      }
    }
    return false;
  }, []);

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [url]); // Only reconnect when URL changes

  return {
    isConnected,
    connectionStatus,
    lastMessage,
    reconnectAttempts,
    connect,
    disconnect,
    sendMessage,
  };
};

// Specific hook for dashboard real-time updates
export const useDashboardWebSocket = () => {
  const [realTimeData, setRealTimeData] = useState<any>(null);
  const [metrics, setMetrics] = useState<any>(null);
  const [alerts, setAlerts] = useState<any[]>([]);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      // FIX: Handle 'INITIAL_STATE' and 'DASHBOARD_STATE_UPDATE'
      case 'INITIAL_STATE':
      case 'DASHBOARD_STATE_UPDATE':
        setRealTimeData(message.payload);
        break;
      case 'metrics_update':
        setMetrics(message.payload);
        break;
      case 'alert':
        setAlerts(prev => [...prev, message.payload]);
        break;
      case 'pattern_update':
        // Handle pattern analysis updates
        console.log('Pattern update received:', message.payload);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, []);

  const webSocket = useWebSocket({
    onMessage: handleMessage,
    onConnect: () => {
      console.log('Dashboard WebSocket connected');
    },
    onDisconnect: () => {
      console.log('Dashboard WebSocket disconnected');
    },
    onError: (error) => {
      console.error('Dashboard WebSocket error:', error);
    },
  });

  const requestDashboardUpdate = useCallback(() => {
    return webSocket.sendMessage({
      type: 'request_dashboard_update',
    });
  }, [webSocket]);

  const requestMetricsUpdate = useCallback(() => {
    return webSocket.sendMessage({
      type: 'request_metrics_update',
    });
  }, [webSocket]);

  return {
    ...webSocket,
    realTimeData,
    metrics,
    alerts,
    requestDashboardUpdate,
    requestMetricsUpdate,
  };
};
