/**
 * LaundrySense Insight Generation Types
 * 
 * Type definitions for the Smart Insight Generator system
 */

import { type CurrentContextObject, type PatternData } from '../types';

// ============================================================================
// INPUT TYPES
// ============================================================================

export interface RealTimeData {
  transactions: {
    today_count: number;
    today_revenue: number;
    current_hour_count: number;
    average_transaction_value: number;
    peak_hour_today: number | null;
    growth_percentage_vs_yesterday?: number; // Added
    new_orders_last_hour?: number; // Added
  };
  materials: {
    material_id: string;
    material_name: string;
    current_stock: number;
    minimum_threshold: number;
    category: string;
    last_restock_date: string;
    usage_rate_per_day: number;
    days_until_empty: number;
  }[];
  customers: {
    new_customers_today: number;
    returning_customers_today: number;
    pending_orders: number;
    // overdue_pickups: number; // Replaced by top-level overduePickupItems
  };
  // Added for the new overdue pickups insight template
  overduePickupItems: OverduePickupItem[]; 
  operations: {
    active_machines: number;
    total_machines: number;
    queue_length: number;
    estimated_completion_times: number[];
    staff_on_duty: number;
  };
}

// Structure for an individual overdue pickup item
export interface OverduePickupItem {
  transactionId: string;
  customerName: string;
  customerPhone: string;
  serviceType: string;
  weightKg: number;
  pickupDate: string; // ISO date string
  daysOverdue: number;
  // Add any other fields from your API response that might be useful for insights
}

export interface InsightGenerationInput {
  contextObject: CurrentContextObject;
  patternData: PatternData;
  realTimeData: RealTimeData;
  timestamp: Date;
}

// ============================================================================
// OUTPUT TYPES
// ============================================================================

export type InsightPriority = 'high' | 'medium' | 'low' | 'normal' | 'critical';
export type InsightCategory = 
  | 'revenue_target'
  | 'inventory_management'
  | 'customer_service'
  | 'operational_efficiency'
  | 'data_quality'
  | 'growth_opportunity'
  | 'cost_optimization'
  | 'staff_management'
  | 'operational_planning'
  | 'operational_monitoring'
  | 'business_review'
  | 'data_integrity';

export interface GeneratedInsight {
  id: string;
  priority: InsightPriority;
  category: InsightCategory;
  title: string;
  context: string;
  action: string;
  impact: string;
  timeEstimate: string;
  relatedData?: Record<string, any>;
  confidence: number; // 0-1
  expiresAt?: Date;
  tags: string[];
  actionLink?: string;
}

export interface InsightGenerationResult {
  insights: GeneratedInsight[];
  totalGenerated: number;
  highPriorityCount: number;
  mediumPriorityCount: number;
  lowPriorityCount: number;
  generationTimestamp: Date;
  contextSummary: {
    primaryContext: string;
    secondaryFactors: string[];
    dataQuality: string;
  };
}

// ============================================================================
// TEMPLATE TYPES
// ============================================================================

export interface InsightTemplate {
  id: string;
  category: InsightCategory;
  priority: InsightPriority;
  title: string;
  contextTemplate: string;
  actionTemplate: string;
  impactTemplate: string;
  timeEstimate: string;
  applicableContexts: {
    time?: string[];
    businessCycle?: string[];
    userBehavior?: string[];
    dataQuality?: string[];
  };
  requiredData: {
    patternData?: string[];
    realTimeData?: string[];
    contextObject?: string[];
  };
  conditions: {
    field: string;
    operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte' | 'contains' | 'exists';
    value: any;
  }[];
  // New property for mapping simple placeholder names to complex data paths
  variables?: Record<string, string>;
  tags: string[];
}

export interface TemplateVariable {
  name: string;
  source: 'patternData' | 'realTimeData' | 'contextObject' | 'calculated';
  path: string;
  formatter?: 'currency' | 'percentage' | 'number' | 'date' | 'time';
  defaultValue?: any;
}

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

export interface InsightGeneratorConfig {
  maxInsightsPerGeneration: number;
  priorityWeights: {
    high: number;
    medium: number;
    low: number;
  };
  contextualFactors: {
    timeWeight: number;
    businessCycleWeight: number;
    userBehaviorWeight: number;
    dataQualityWeight: number;
  };
  thresholds: {
    lowStockDays: number;
    highRevenueVariance: number;
    customerRetentionThreshold: number;
    dataQualityMinimum: number;
  };
  formatting: {
    currency: string;
    locale: string;
    timezone: string;
  };
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export interface InsightMetrics {
  relevanceScore: number;
  urgencyScore: number;
  impactScore: number;
  confidenceScore: number;
  overallScore: number;
}

export interface TemplateMatchResult {
  template: InsightTemplate;
  matchScore: number;
  missingData: string[];
  variableValues: Record<string, any>;
}

export interface InsightValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// ============================================================================
// PROGRESSIVE DISCLOSURE TYPES
// ============================================================================

export interface InsightLayer {
  level: 'headline' | 'context' | 'action' | 'impact' | 'details';
  content: string;
  data?: Record<string, any>;
  isVisible: boolean;
}

export interface ProgressiveInsight {
  id: string;
  layers: InsightLayer[];
  currentLevel: number;
  maxLevel: number;
  userInteractions: {
    views: number;
    expansions: number;
    actions_taken: number;
    last_viewed: Date;
  };
}

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

export interface InsightAnalytics {
  generationStats: {
    total_generated: number;
    by_priority: Record<InsightPriority, number>;
    by_category: Record<InsightCategory, number>;
    by_context: Record<string, number>;
  };
  userEngagement: {
    total_views: number;
    total_expansions: number;
    total_actions: number;
    engagement_rate: number;
    most_viewed_categories: string[];
  };
  effectiveness: {
    action_completion_rate: number;
    impact_realization_rate: number;
    user_satisfaction_score: number;
    most_effective_templates: string[];
  };
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface InsightGenerationError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
  context?: Partial<InsightGenerationInput>;
}
