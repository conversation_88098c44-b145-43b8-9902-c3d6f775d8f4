# 3. Imple<PERSON><PERSON> <PERSON><PERSON> (`Contextual Engine`)

Dokumen ini merinci arsitektur dan alur kerja dari `Contextual Engine` yang terletak di `src/lib/contextual-intelligence`. Mesin ini adalah otak di balik "Smart Contextual Dashboard", yang bertanggung jawab untuk mengubah data mentah menjadi wawasan yang cerdas dan dapat ditindaklanjuti.

## 3.1. Filosofi Desain

Tujuan utama dari `Contextual Engine` adalah untuk meniru kemampuan seorang manajer berpengalaman yang dapat "merasakan" situasi bisnis saat ini dan memberikan arahan yang tepat. Ini dicapai dengan dua langkah utama:
1.  **Memahami Konteks:** Per<PERSON>a, sistem harus memahami apa yang sedang terjadi. Apakah ini pagi yang sibuk? Apakah ada data analisis yang hilang? Apakah ada pelanggan penting yang sudah lama tidak datang?
2.  **<PERSON><PERSON><PERSON><PERSON><PERSON>:** <PERSON><PERSON><PERSON> konteks dipahami, sistem harus menghasilkan rekomendasi atau informasi yang paling relevan dengan situasi tersebut.

## 3.2. Alur Kerja Engine

Alur kerja dibagi menjadi dua komponen utama yang bekerja secara berurutan: `ContextDetector` dan `SmartInsightGenerator`.

```
+-----------------+      +-----------------+      +-------------------------+      +-----------------------+
|   Data Input    |----->| ContextDetector |----->|      SystemContext      |----->| SmartInsightGenerator |-----> Wawasan
| (Real-time &    |      | (w/ Analyzers)  |      | (e.g., {time: 'pagi'})  |      | (w/ InsightTemplates) |      (Untuk UI)
|  Historis)      |      +-----------------+      +-------------------------+      +-----------------------+
+-----------------+
```

### 3.2.1. Langkah 1: Deteksi Konteks dengan `ContextDetector`

`ContextDetector` adalah titik awal. Tugasnya adalah mengumpulkan sinyal dari berbagai bagian aplikasi dan mensintesisnya menjadi sebuah objek `SystemContext` yang komprehensif. Untuk melakukan ini, ia menggunakan serangkaian `Analyzer` modular, di mana setiap `Analyzer` bertanggung jawab untuk menilai satu aspek spesifik dari operasi bisnis.

Beberapa `Analyzer` kunci meliputi:

*   **`TimeAnalyzer`**: Menganalisis waktu dan tanggal saat ini.
    *   **Output:** Menghasilkan konteks seperti `time: 'morning'`, `day: 'weekend'`, `period: 'end_of_month'`.
*   **`BusinessCycleAnalyzer`**: Memanfaatkan data historis dari `PatternAnalysisService` untuk memahami ritme bisnis.
    *   **Output:** Menghasilkan konteks seperti `cycle: 'peak_hour'`, `trend: 'customer_influx_expected'`.
*   **`DataQualityAnalyzer`**: Memeriksa kesegaran dan kelengkapan data analitik yang menjadi dasar wawasan.
    *   **Output:** Menghasilkan konteks seperti `data_quality: 'stale'`, `status: 'patterns_not_calculated'`. Ini penting untuk mencegah sistem memberikan wawasan berdasarkan data yang usang.
*   **`UserBehaviorAnalyzer`**: (Dapat diperluas) Menganalisis interaksi pengguna di aplikasi untuk personalisasi lebih lanjut.

`ContextDetector` menjalankan semua `Analyzer` ini dan menggabungkan hasilnya menjadi satu objek `SystemContext` tunggal. Contoh:
```json
{
  "timeContext": "weekday_morning",
  "businessContext": "normal_traffic",
  "dataContext": "fresh",
  "activeAlerts": ["low_stock_warning"]
}
```

### 3.2.2. Langkah 2: Generasi Wawasan dengan `SmartInsightGenerator`

Setelah `SystemContext` ditetapkan, `SmartInsightGenerator` mengambil alih. Tugasnya adalah mencocokkan konteks saat ini dengan serangkaian `InsightTemplate` yang telah ditentukan sebelumnya.

*   **`InsightTemplate`**: Ini adalah cetak biru untuk sebuah wawasan. Setiap template mendefinisikan:
    *   **Kondisi Pemicu (Triggering Context):** Konteks spesifik apa yang harus aktif agar wawasan ini relevan. Misalnya, `activeAlerts` harus berisi `'low_stock_warning'`.
    *   **Konten Pesan:** Teks dinamis yang dapat diisi dengan data real-time. Contoh: `"Stok {materialName} hampir habis! Tersisa {stockLevel}."`
    *   **Tingkat Kepentingan:** Prioritas wawasan (misalnya, `critical`, `info`).
    *   **Tindakan yang Disarankan (Actionable Call-to-Action):** Tombol atau tautan yang memungkinkan pengguna untuk segera bertindak. Contoh: `{"label": "Buat Pesanan", "action": "NAVIGATE_TO_PURCHASE_ORDER"}`.

`SmartInsightGenerator` melakukan iterasi melalui semua `InsightTemplate` yang tersedia dan memilih yang kondisinya cocok dengan `SystemContext` saat ini. Kemudian, ia mengambil data yang relevan (misalnya, nama material dan level stok dari database) untuk mengisi template dan menghasilkan objek `Insight` final yang siap ditampilkan di `ProgressiveInsightCard` pada antarmuka pengguna.

## 3.3. Kesimpulan

Dengan memisahkan deteksi konteks dari generasi wawasan, arsitektur ini sangat modular dan dapat diperluas. `Analyzer` baru dapat ditambahkan untuk mendeteksi konteks yang lebih canggih, dan `InsightTemplate` baru dapat dibuat untuk merespons konteks tersebut tanpa mengubah logika inti. Hasilnya adalah sistem yang cerdas, relevan, dan benar-benar membantu pengguna dalam pekerjaan sehari-hari mereka.