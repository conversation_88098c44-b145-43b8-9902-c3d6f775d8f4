# 🔄 LaundrySeense - Sequence Diagrams

## 📊 **DASH<PERSON>ARD LOADING SEQUENCE**

```mermaid
sequenceDiagram
    participant User
    participant DashboardPage
    participant DashboardContext
    participant ContextDetector
    participant SmartInsightGenerator
    participant WebSocket
    participant API
    participant Database
    
    User->>DashboardPage: Open Dashboard
    DashboardPage->>DashboardContext: Initialize Context
    DashboardContext->>ContextDetector: detectContext(timestamp)
    ContextDetector-->>DashboardContext: CurrentContextObject
    
    DashboardContext->>API: GET /api/kpis/real-time
    API->>Database: Query real-time metrics
    Database-->>API: KPI data
    API-->>DashboardContext: Real-time KPIs
    
    DashboardContext->>API: GET /api/patterns/all
    API->>Database: Query pattern data
    Database-->>API: Pattern data
    API-->>DashboardContext: Pattern data
    
    DashboardContext->>SmartInsightGenerator: generateInsights(input)
    SmartInsightGenerator->>SmartInsightGenerator: analyzeRevenueTrends()
    SmartInsightGenerator->>SmartInsightGenerator: analyzeCustomerPatterns()
    SmartInsightGenerator->>SmartInsightGenerator: analyzeMaterialUsage()
    SmartInsightGenerator-->>DashboardContext: Generated insights
    
    DashboardContext-->>DashboardPage: Dashboard data ready
    DashboardPage-->>User: Display dashboard
    
    Note over WebSocket: Real-time updates
    WebSocket->>DashboardContext: Data update event
    DashboardContext->>API: GET /api/kpis/real-time
    API-->>DashboardContext: Updated KPIs
    DashboardContext-->>DashboardPage: Update dashboard
    DashboardPage-->>User: Refresh display
```

## 👤 **CUSTOMER CREATION SEQUENCE**

```mermaid
sequenceDiagram
    participant User
    participant EnhancedCustomerForm
    participant useCustomers
    participant CustomerAPI
    participant ZodValidator
    participant Database
    
    User->>EnhancedCustomerForm: Click "Add Customer"
    EnhancedCustomerForm-->>User: Show enhanced form
    
    User->>EnhancedCustomerForm: Fill customer data
    EnhancedCustomerForm->>EnhancedCustomerForm: validateForm()
    EnhancedCustomerForm->>EnhancedCustomerForm: formatPhone()
    
    alt Validation Failed
        EnhancedCustomerForm-->>User: Show validation errors
    else Validation Passed
        User->>EnhancedCustomerForm: Submit form
        EnhancedCustomerForm->>useCustomers: createCustomer(data)
        useCustomers->>CustomerAPI: POST /api/customers
        
        CustomerAPI->>ZodValidator: validate(customerData)
        alt Validation Failed
            ZodValidator-->>CustomerAPI: Validation errors
            CustomerAPI-->>useCustomers: 400 Bad Request
            useCustomers-->>EnhancedCustomerForm: Error response
            EnhancedCustomerForm-->>User: Show server errors
        else Validation Passed
            ZodValidator-->>CustomerAPI: Valid data
            CustomerAPI->>Database: Check phone uniqueness
            
            alt Phone Already Exists
                Database-->>CustomerAPI: Duplicate found
                CustomerAPI-->>useCustomers: 400 Conflict
                useCustomers-->>EnhancedCustomerForm: Duplicate error
                EnhancedCustomerForm-->>User: Show duplicate error
            else Phone Unique
                Database-->>CustomerAPI: No duplicate
                CustomerAPI->>Database: INSERT customer
                Database-->>CustomerAPI: Customer created
                CustomerAPI-->>useCustomers: 201 Created
                useCustomers-->>EnhancedCustomerForm: Success response
                EnhancedCustomerForm-->>User: Show success message
                EnhancedCustomerForm->>EnhancedCustomerForm: Reset form
            end
        end
    end
```

## 💰 **TRANSACTION CREATION SEQUENCE**

```mermaid
sequenceDiagram
    participant User
    participant EnhancedTransactionForm
    participant useTransactions
    participant useCustomers
    participant TransactionAPI
    participant CustomerAPI
    participant SmartCalculator
    participant ZodValidator
    participant Database
    participant WebSocket
    
    User->>EnhancedTransactionForm: Click "Add Transaction"
    EnhancedTransactionForm-->>User: Show enhanced form
    
    User->>EnhancedTransactionForm: Search customer
    EnhancedTransactionForm->>useCustomers: getCustomersList()
    useCustomers->>CustomerAPI: GET /api/customers
    CustomerAPI->>Database: Query customers
    Database-->>CustomerAPI: Customer list
    CustomerAPI-->>useCustomers: Customer data
    useCustomers-->>EnhancedTransactionForm: Customer options
    EnhancedTransactionForm-->>User: Show customer dropdown
    
    User->>EnhancedTransactionForm: Select customer
    User->>EnhancedTransactionForm: Select service & weight
    EnhancedTransactionForm->>SmartCalculator: calculatePrice(service, weight, weather)
    SmartCalculator->>SmartCalculator: getBasePrice(service)
    SmartCalculator->>SmartCalculator: applyWeightMultiplier(weight)
    SmartCalculator->>SmartCalculator: applyWeatherAdjustment(weather)
    SmartCalculator-->>EnhancedTransactionForm: Calculated price
    EnhancedTransactionForm-->>User: Show price breakdown
    
    User->>EnhancedTransactionForm: Set pickup date
    EnhancedTransactionForm->>SmartCalculator: calculateDeliveryDate(service, pickup)
    SmartCalculator-->>EnhancedTransactionForm: Estimated delivery
    EnhancedTransactionForm-->>User: Show delivery estimate
    
    User->>EnhancedTransactionForm: Submit transaction
    EnhancedTransactionForm->>useTransactions: createTransaction(data)
    useTransactions->>TransactionAPI: POST /api/transactions
    
    TransactionAPI->>ZodValidator: validate(transactionData)
    alt Validation Failed
        ZodValidator-->>TransactionAPI: Validation errors
        TransactionAPI-->>useTransactions: 400 Bad Request
        useTransactions-->>EnhancedTransactionForm: Error response
        EnhancedTransactionForm-->>User: Show validation errors
    else Validation Passed
        ZodValidator-->>TransactionAPI: Valid data
        TransactionAPI->>Database: Check customer exists
        
        alt Customer Not Found
            Database-->>TransactionAPI: Customer not found
            TransactionAPI-->>useTransactions: 400 Bad Request
            useTransactions-->>EnhancedTransactionForm: Customer error
            EnhancedTransactionForm-->>User: Show customer error
        else Customer Found
            Database-->>TransactionAPI: Customer exists
            TransactionAPI->>Database: BEGIN transaction
            TransactionAPI->>Database: INSERT transaction
            TransactionAPI->>Database: UPDATE material usage
            TransactionAPI->>Database: COMMIT transaction
            Database-->>TransactionAPI: Transaction created
            
            TransactionAPI->>WebSocket: Broadcast update
            WebSocket-->>DashboardContext: New transaction event
            
            TransactionAPI-->>useTransactions: 201 Created
            useTransactions-->>EnhancedTransactionForm: Success response
            EnhancedTransactionForm-->>User: Show success message
            EnhancedTransactionForm->>EnhancedTransactionForm: Reset form
        end
    end
```

## 📦 **MATERIAL STOCK UPDATE SEQUENCE**

```mermaid
sequenceDiagram
    participant User
    participant EnhancedMaterialForm
    participant useMaterials
    participant MaterialAPI
    participant ZodValidator
    participant Database
    participant AlertSystem
    participant WebSocket

    User->>EnhancedMaterialForm: Select material to update
    EnhancedMaterialForm->>useMaterials: getMaterial(id)
    useMaterials->>MaterialAPI: GET /api/materials/{id}
    MaterialAPI->>Database: Query material data
    Database-->>MaterialAPI: Material data
    MaterialAPI-->>useMaterials: Material response
    useMaterials-->>EnhancedMaterialForm: Current material data
    EnhancedMaterialForm-->>User: Show pre-filled form

    User->>EnhancedMaterialForm: Update stock level
    EnhancedMaterialForm->>EnhancedMaterialForm: calculateStockStatus()
    EnhancedMaterialForm-->>User: Show new stock status indicator

    User->>EnhancedMaterialForm: Submit update
    EnhancedMaterialForm->>useMaterials: updateMaterial(id, data)
    useMaterials->>MaterialAPI: PUT /api/materials/{id}

    MaterialAPI->>ZodValidator: validate(materialData)
    alt Validation Failed
        ZodValidator-->>MaterialAPI: Validation errors
        MaterialAPI-->>useMaterials: 400 Bad Request
        useMaterials-->>EnhancedMaterialForm: Error response
        EnhancedMaterialForm-->>User: Show validation errors
    else Validation Passed
        ZodValidator-->>MaterialAPI: Valid data
        MaterialAPI->>Database: UPDATE material
        Database-->>MaterialAPI: Material updated

        MaterialAPI->>MaterialAPI: checkLowStockAlert()
        alt Stock Below Threshold
            MaterialAPI->>AlertSystem: triggerLowStockAlert()
            AlertSystem->>WebSocket: Broadcast low stock alert
            WebSocket-->>DashboardContext: Low stock event
        end

        MaterialAPI-->>useMaterials: 200 Success
        useMaterials-->>EnhancedMaterialForm: Success response
        EnhancedMaterialForm-->>User: Show success message
    end
```

## 📈 **PATTERN CALCULATION SEQUENCE**

```mermaid
sequenceDiagram
    participant CronJob
    participant PatternCalculationJob
    participant Database
    participant CustomerPattern
    participant MaterialUsagePattern
    participant RevenueTrend
    participant WebSocket

    Note over CronJob: Daily 2:00 AM
    CronJob->>PatternCalculationJob: Execute pattern calculation

    PatternCalculationJob->>Database: Query customers with transactions
    Database-->>PatternCalculationJob: Customer transaction data

    loop For each customer
        PatternCalculationJob->>PatternCalculationJob: calculateFrequency()
        PatternCalculationJob->>PatternCalculationJob: analyzePreferences()
        PatternCalculationJob->>PatternCalculationJob: calculateSeasonality()
        PatternCalculationJob->>PatternCalculationJob: calculateLoyaltyScore()
        PatternCalculationJob->>CustomerPattern: UPSERT pattern data
        CustomerPattern->>Database: Save customer pattern
    end

    PatternCalculationJob->>Database: Query materials with usage
    Database-->>PatternCalculationJob: Material usage data

    loop For each material
        PatternCalculationJob->>PatternCalculationJob: calculateConsumptionRate()
        PatternCalculationJob->>PatternCalculationJob: analyzeUsageTrends()
        PatternCalculationJob->>PatternCalculationJob: predictStockDepletion()
        PatternCalculationJob->>PatternCalculationJob: generateReorderRecommendation()
        PatternCalculationJob->>MaterialUsagePattern: UPSERT pattern data
        MaterialUsagePattern->>Database: Save usage pattern
    end

    PatternCalculationJob->>Database: Query daily transactions
    Database-->>PatternCalculationJob: Daily transaction data

    loop For each day
        PatternCalculationJob->>PatternCalculationJob: calculateDailyRevenue()
        PatternCalculationJob->>PatternCalculationJob: analyzeTrends()
        PatternCalculationJob->>PatternCalculationJob: detectPeaks()
        PatternCalculationJob->>RevenueTrend: UPSERT trend data
        RevenueTrend->>Database: Save revenue trend
    end

    PatternCalculationJob->>WebSocket: Broadcast pattern update
    WebSocket-->>DashboardContext: Pattern calculation complete
    PatternCalculationJob-->>CronJob: Calculation complete
```

## 🔍 **SEQUENCE EXPLANATIONS**

### **Dashboard Loading Sequence**
- **Context Detection**: System mendeteksi current context (time, business cycle) untuk adaptive behavior
- **Parallel Data Loading**: KPIs dan patterns dimuat secara parallel untuk performance
- **Insight Generation**: AI engine menganalisis data dan menghasilkan actionable insights
- **Real-time Updates**: WebSocket connection memungkinkan live updates tanpa refresh

### **Customer Creation Sequence**
- **Multi-layer Validation**: Client-side validation untuk UX, server-side untuk security
- **Phone Formatting**: Real-time phone number formatting untuk consistency
- **Uniqueness Check**: Database constraint enforcement untuk data integrity
- **Error Handling**: Comprehensive error handling dengan user-friendly messages

### **Transaction Creation Sequence**
- **Smart Customer Search**: Fuzzy search untuk easy customer discovery
- **Real-time Calculations**: Auto-calculation pricing berdasarkan multiple factors
- **Contextual Intelligence**: Weather dan time-based pricing adjustments
- **Database Transactions**: ACID compliance untuk data consistency
- **WebSocket Broadcasting**: Real-time updates untuk dashboard

### **Material Stock Update Sequence**
- **Stock Status Calculation**: Real-time status calculation dengan visual indicators
- **Alert Integration**: Automatic low stock alerts dengan WebSocket broadcasting
- **Validation Pipeline**: Multi-layer validation untuk data integrity
- **Real-time Feedback**: Immediate user feedback untuk all operations

### **Pattern Calculation Sequence**
- **Scheduled Execution**: Cron-based scheduling untuk automated analysis
- **Batch Processing**: Efficient processing untuk large datasets
- **Multiple Pattern Types**: Customer, material, dan revenue pattern analysis
- **UPSERT Operations**: Efficient database operations untuk pattern updates
- **Completion Broadcasting**: WebSocket notification untuk pattern completion

## 🎯 **KEY DESIGN PATTERNS**

### **Request-Response Pattern**
- Consistent API structure dengan proper HTTP status codes
- Standardized error responses dengan detailed messages
- Type-safe validation dengan Zod schemas

### **Observer Pattern**
- WebSocket broadcasting untuk real-time updates
- Event-driven architecture untuk loose coupling
- Dashboard context sebagai central observer

### **Strategy Pattern**
- Multiple calculation strategies untuk different contexts
- Pluggable validation strategies
- Adaptive UI behavior berdasarkan context

### **Command Pattern**
- Form submissions sebagai commands
- Database transactions sebagai atomic commands
- Background jobs sebagai scheduled commands

## 🚀 **PERFORMANCE CONSIDERATIONS**

### **Async Operations**
- Non-blocking API calls dengan proper loading states
- Parallel data loading untuk dashboard initialization
- Background processing untuk heavy calculations

### **Error Recovery**
- Graceful degradation untuk network failures
- Retry mechanisms untuk transient errors
- User-friendly error messages dengan recovery suggestions

### **Data Consistency**
- Database transactions untuk atomic operations
- Optimistic locking untuk concurrent updates
- Real-time synchronization dengan WebSocket
