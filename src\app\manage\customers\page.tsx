"use client";

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useCustomers } from '@/hooks/useApi';
import DataTable from '@/components/ui/DataTable';
import Modal from '@/components/ui/Modal';
import <PERSON>Field from '@/components/ui/FormField';
import EnhancedCustomerForm from '@/components/forms/EnhancedCustomerForm';
import { User, Phone, Mail, Calendar } from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  address?: string;
  registration_date: string;
  last_transaction_date?: string;
  _count?: {
    transactions: number;
  };
  customerPattern?: {
    frequency_score: number;
  } | null;
}

interface CustomerFormData {
  name: string;
  phone_number: string;
  email: string;
  address: string;
}

interface CustomerFormErrors {
  name?: string;
  phone_number?: string;
  email?: string;
  address?: string;
}

const CustomersPage: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [useEnhancedForm, setUseEnhancedForm] = useState(true);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    phone_number: '',
    email: '',
    address: '',
  });
  const [formErrors, setFormErrors] = useState<CustomerFormErrors>({});

  const {
    loading,
    error,
    getCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
  } = useCustomers();

  const [isLoading, setIsLoading] = useState(false);

  const loadCustomers = useCallback(async () => {
    if (isLoading) return; // Prevent multiple simultaneous calls

    setIsLoading(true);
    try {
      const params = {
        page: currentPage,
        limit: itemsPerPage,
      };

      const response = await getCustomers(params);
      if (response.success && response.data) {
        setCustomers(response.data.customers);
        setTotalCustomers(response.data.total);
      }
    } finally {
      setIsLoading(false);
    }
  }, [getCustomers, currentPage, itemsPerPage, isLoading]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadCustomers();
    }, 100); // Debounce to prevent rapid calls

    return () => clearTimeout(timeoutId);
  }, [currentPage, itemsPerPage]); // Remove loadCustomers from dependencies

  const validateForm = (): boolean => {
    const errors: CustomerFormErrors = {};

    if (!formData.name.trim()) {
      errors.name = 'Nama wajib diisi';
    }

    if (!formData.phone_number.trim()) {
      errors.phone_number = 'Nomor telepon wajib diisi';
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone_number)) {
      errors.phone_number = 'Format nomor telepon tidak valid';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Format email tidak valid';
    }


    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    const submitData = {
      ...formData,
      email: formData.email || null,
      address: formData.address || null,
    };

    let response;
    if (editingCustomer) {
      response = await updateCustomer(editingCustomer.id, submitData);
    } else {
      response = await createCustomer(submitData);
    }

    if (response.success) {
      setIsModalOpen(false);
      resetForm();
      // Trigger refresh by updating currentPage if needed, or just reload current page
      loadCustomers();
    }
  };

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      phone_number: customer.phone_number,
      email: customer.email || '',
      address: customer.address || '',
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (customer: Customer) => {
    if (window.confirm(`Apakah Anda yakin ingin menghapus pelanggan ${customer.name}?`)) {
      const response = await deleteCustomer(customer.id);
      if (response.success) {
        loadCustomers(); // Refresh current page
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      phone_number: '',
      email: '',
      address: '',
    });
    setFormErrors({});
    setEditingCustomer(null);
  };

  const handleCreate = () => {
    resetForm();
    setIsModalOpen(true);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (size: number) => {
    setItemsPerPage(size);
    setCurrentPage(1); // Reset to first page
  };

  const columns = [
    {
      key: 'name',
      label: 'Nama',
      sortable: true,
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <User size={16} className="text-gray-400" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'phone_number',
      label: 'Telepon',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Phone size={16} className="text-gray-400" />
          <span>{value}</span>
        </div>
      ),
    },
    {
      key: 'email',
      label: 'Email',
      render: (value: string) => (
        value ? (
          <div className="flex items-center gap-2">
            <Mail size={16} className="text-gray-400" />
            <span>{value}</span>
          </div>
        ) : (
          <span className="text-gray-400">-</span>
        )
      ),
    },
    {
      key: '_count',
      label: 'Transaksi',
      render: (value: any) => (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
          {value?.transactions || 0}
        </span>
      ),
    },
    {
      key: 'registration_date',
      label: 'Terdaftar',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Calendar size={16} className="text-gray-400" />
          <span className="text-sm">{new Date(value).toLocaleDateString('id-ID')}</span>
        </div>
      ),
    },
    {
      key: 'customerPattern',
      label: 'Skor Frekuensi',
      render: (value: any, record: Customer) => {
        const score = record.customerPattern?.frequency_score;
        if (score === null || score === undefined) {
          return <span className="text-gray-400">N/A</span>;
        }
        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            score > 0.7 ? 'bg-green-100 text-green-800' :
            score > 0.4 ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {score.toFixed(2)}
          </span>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manajemen Pelanggan</h1>
          <div className="mt-2 flex items-center gap-4">
            <label className="flex items-center gap-2 text-sm text-gray-600">
              <input
                type="checkbox"
                checked={useEnhancedForm}
                onChange={(e) => setUseEnhancedForm(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              Gunakan Form Enhanced (Recommended)
            </label>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <DataTable
        title="Daftar Pelanggan"
        columns={columns}
        data={customers}
        loading={loading || isLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onCreate={handleCreate}
        emptyMessage="Belum ada pelanggan terdaftar"
        totalItems={totalCustomers}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        itemsPerPageOptions={[15, 20, 25]}
      />

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingCustomer ? 'Edit Pelanggan' : 'Tambah Pelanggan'}
        size={useEnhancedForm ? "full" : "lg"}
      >
        {useEnhancedForm ? (
          <EnhancedCustomerForm
            initialData={editingCustomer ? {
              name: editingCustomer.name,
              phone_number: editingCustomer.phone_number,
              email: editingCustomer.email || '',
              address: editingCustomer.address || '',
              notes: '',
            } : undefined}
            onSubmit={async (data) => {
              const submitData = {
                ...data,
                email: data.email || null,
                address: data.address || null,
              };

              const response = editingCustomer
                ? await updateCustomer(editingCustomer.id, submitData)
                : await createCustomer(submitData);

              if (response.success) {
                setIsModalOpen(false);
                resetForm();
                loadCustomers();
              }
            }}
            onCancel={() => setIsModalOpen(false)}
            isLoading={loading}
            isEditing={!!editingCustomer}
          />
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Nama"
              name="name"
              value={formData.name}
              onChange={(value) => setFormData({ ...formData, name: value as string })}
              error={formErrors.name}
              required
            />

            <FormField
              label="Nomor Telepon"
              name="phone_number"
              type="tel"
              value={formData.phone_number}
              onChange={(value) => setFormData({ ...formData, phone_number: value as string })}
              error={formErrors.phone_number}
              required
            />

            <FormField
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={(value) => setFormData({ ...formData, email: value as string })}
              error={formErrors.email}
            />

            <FormField
              label="Alamat"
              name="address"
              type="textarea"
              value={formData.address}
              onChange={(value) => setFormData({ ...formData, address: value as string })}
              rows={2}
            />

          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Batal
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Menyimpan...' : editingCustomer ? 'Update' : 'Simpan'}
            </button>
          </div>
        </form>
        )}
      </Modal>
    </div>
  );
};

export default CustomersPage;
