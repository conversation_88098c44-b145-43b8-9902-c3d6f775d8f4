# 🏗️ LaundrySeense - Architecture Diagram

## 📐 **SYSTEM ARCHITECTURE**

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Web Browser]
        Mobile[Mobile Browser]
    end

    subgraph "Frontend Layer"
        NextJS[Next.js 15 App]
        React[React 19 Components]
        Context[React Context]
        Hooks[Custom Hooks]
        Forms[Enhanced Forms]
    end

    subgraph "Backend Layer"
        API[API Routes]
        Validation[Zod Validation]
        Prisma[Prisma ORM]
        WebSocket[WebSocket Server]
    end

    subgraph "Intelligence Layer"
        ContextDetector[Context Detector]
        InsightGenerator[Insight Generator]
        PatternEngine[Pattern Analysis]
        SmartCalculator[Smart Calculator]
    end

    subgraph "Data Layer"
        MySQL[(MySQL Database)]
        Patterns[(Pattern Data)]
        RealTime[(Real-time Cache)]
    end

    subgraph "Background Services"
        CronJobs[Pattern Calculation Jobs]
        DataProcessor[Data Processing]
        AlertSystem[Alert System]
    end

    %% Client to Frontend
    UI --> NextJS
    Mobile --> NextJS

    %% Frontend Internal
    NextJS --> React
    React --> Context
    React --> Hooks
    React --> Forms
    Context --> Hooks

    %% Frontend to Backend
    Hooks --> API
    Forms --> API
    Context --> WebSocket

    %% Backend Internal
    API --> Validation
    Validation --> Prisma
    WebSocket --> RealTime

    %% Backend to Intelligence
    API --> ContextDetector
    API --> InsightGenerator
    Prisma --> PatternEngine
    PatternEngine --> SmartCalculator

    %% Intelligence to Data
    ContextDetector --> RealTime
    InsightGenerator --> Patterns
    PatternEngine --> MySQL
    SmartCalculator --> MySQL

    %% Backend to Data
    Prisma --> MySQL
    WebSocket --> RealTime

    %% Background Services
    CronJobs --> PatternEngine
    DataProcessor --> MySQL
    AlertSystem --> RealTime
    CronJobs --> DataProcessor

    %% Styling
    classDef frontend fill:#3b82f6,stroke:#1e40af,stroke-width:2px,color:#fff
    classDef backend fill:#10b981,stroke:#047857,stroke-width:2px,color:#fff
    classDef intelligence fill:#8b5cf6,stroke:#5b21b6,stroke-width:2px,color:#fff
    classDef data fill:#f59e0b,stroke:#d97706,stroke-width:2px,color:#fff
    classDef services fill:#ef4444,stroke:#dc2626,stroke-width:2px,color:#fff

    class NextJS,React,Context,Hooks,Forms frontend
    class API,Validation,Prisma,WebSocket backend
    class ContextDetector,InsightGenerator,PatternEngine,SmartCalculator intelligence
    class MySQL,Patterns,RealTime data
    class CronJobs,DataProcessor,AlertSystem services
```

## 🔧 **LAYER EXPLANATIONS**

### **1. Client Layer**
- **Web Browser**: Desktop users mengakses aplikasi melalui modern browsers
- **Mobile Browser**: Mobile users dengan responsive design yang optimal

### **2. Frontend Layer**
- **Next.js 15**: Framework React dengan App Router dan server-side rendering
- **React 19**: Component-based UI dengan hooks dan modern patterns
- **React Context**: Global state management untuk dashboard data
- **Custom Hooks**: Reusable logic untuk API calls dan data management
- **Enhanced Forms**: Smart forms dengan validation dan real-time features

### **3. Backend Layer**
- **API Routes**: RESTful endpoints untuk CRUD operations
- **Zod Validation**: Type-safe validation untuk request/response
- **Prisma ORM**: Database abstraction dengan type safety
- **WebSocket Server**: Real-time communication untuk live updates

### **4. Intelligence Layer**
- **Context Detector**: Mendeteksi konteks bisnis (time, weather, patterns)
- **Insight Generator**: Menghasilkan business insights berdasarkan data
- **Pattern Engine**: Analisis pola customer dan material usage
- **Smart Calculator**: Kalkulasi otomatis pricing dan scheduling

### **5. Data Layer**
- **MySQL Database**: Primary data storage dengan relational schema
- **Pattern Data**: Calculated patterns untuk business intelligence
- **Real-time Cache**: In-memory cache untuk WebSocket data

### **6. Background Services**
- **Cron Jobs**: Scheduled tasks untuk pattern calculation
- **Data Processor**: Background processing untuk analytics
- **Alert System**: Monitoring dan notification system

## 🔄 **DATA FLOW PATTERNS**

### **Request Flow**
1. **User Interaction** → Frontend Components
2. **Form Submission** → Custom Hooks
3. **API Call** → Backend Validation
4. **Database Operation** → Prisma ORM
5. **Response** → Frontend Update

### **Real-time Flow**
1. **Data Change** → Background Services
2. **Pattern Calculation** → Intelligence Layer
3. **WebSocket Broadcast** → Frontend Context
4. **UI Update** → React Components

### **Intelligence Flow**
1. **Data Collection** → Pattern Engine
2. **Context Detection** → Smart Calculator
3. **Insight Generation** → Dashboard Display
4. **User Action** → Feedback Loop

## 🚀 **DEPLOYMENT ARCHITECTURE**

### **Integrated Server**
- **Single Port (3000)**: Next.js + WebSocket combined
- **Nodemon**: Auto-restart untuk development
- **Background Jobs**: Pattern calculation dalam server yang sama

### **Scalability Considerations**
- **Horizontal Scaling**: API routes dapat di-scale independently
- **Database Optimization**: Indexed queries dan connection pooling
- **Caching Strategy**: Redis untuk production caching
- **Load Balancing**: Ready untuk multiple server instances

<function_calls>
<invoke name="render-mermaid">
<parameter name="title">LaundrySeense System Architecture
