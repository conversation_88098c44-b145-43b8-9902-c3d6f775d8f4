# 9. Tumpukan Teknologi (Tech Stack)

Dokumen ini memberikan daftar rinci semua teknologi, library, dan dependensi utama yang digunakan dalam proyek `laundrysense`, beserta deskripsi perannya dalam aplikasi. Informasi ini didasarkan pada `package.json` dan analisis arsitektur.

## 9.1. Framework & Runtime

| Teknologi | Versi | Peran dalam Aplikasi |
| :--- | :--- | :--- |
| **Node.js** | - | Lingkungan eksekusi JavaScript di sisi server. Menjalankan backend, API, dan skrip. |
| **Next.js** | 15.3.3 | Framework React utama. Menangani routing, rendering sisi server (SSR), dan pembuatan API. |
| **React** | 19 | Library inti untuk membangun antarmuka pengguna (UI) yang interaktif dan berbasis komponen. |
| **TypeScript** | 5 | Superset dari JavaScript yang menambahkan pengetikan statis. Meningkatkan keamanan tipe dan keterbacaan kode. |

## 9.2. Database & Data

| Teknologi | Versi | <PERSON>an <PERSON> Aplik<PERSON> |
| :--- | :--- | :--- |
| **Prisma** | 6.9.0 | ORM (Object-Relational Mapper) modern untuk TypeScript. Menyediakan akses database yang aman (type-safe) ke MySQL. |
| **MySQL** | - | Sistem manajemen database relasional (RDBMS) yang digunakan untuk menyimpan semua data aplikasi. |
| **Zod** | 3.22.4 | Library validasi skema untuk TypeScript. Digunakan untuk memvalidasi data yang masuk ke API sebelum diproses. |

## 9.3. Antarmuka Pengguna (Frontend)

| Teknologi | Versi | Peran dalam Aplikasi |
| :--- | :--- | :--- |
| **TailwindCSS** | 3.4.1 | Framework CSS utility-first untuk styling cepat dan konsisten tanpa meninggalkan HTML. |
| **Lucide React** | - | Pustaka ikon yang ringan dan mudah disesuaikan, digunakan di seluruh antarmuka pengguna. |

## 9.4. Backend & Komunikasi

| Teknologi | Versi | Peran dalam Aplikasi |
| :--- | :--- | :--- |
| **ws** | 8.18.2 | Library WebSocket untuk Node.js. Digunakan untuk membangun server WebSocket yang menangani komunikasi real-time dengan klien. |
| **node-cron** | 3.0.3 | Penjadwal tugas (cron job) untuk Node.js. Digunakan untuk menjalankan `PatternAnalysisService` secara otomatis pada jadwal tertentu. |

## 9.5. Perkakas Pengembangan & Testing

| Teknologi | Versi | Peran dalam Aplikasi |
| :--- | :--- | :--- |
| **tsx** | - | Peningkatan untuk Node.js yang memungkinkan eksekusi file TypeScript dan ESM secara langsung. |
| **nodemon** | - | Alat pengembangan yang secara otomatis me-restart server Node.js saat mendeteksi perubahan file. |
| **Jest** | 29.7.0 | Framework pengujian (testing) JavaScript. Digunakan untuk unit testing pada logika bisnis dan endpoint API. |
| **ESLint** | - | Alat analisis kode statis untuk mengidentifikasi dan melaporkan pola bermasalah dalam kode JavaScript/TypeScript. |