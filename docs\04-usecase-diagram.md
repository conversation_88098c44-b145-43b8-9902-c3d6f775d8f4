# 👤 LaundrySeense - Use Case Diagram

## 🎯 **SYSTEM USE CASES**

```mermaid
graph TB
    User((User))
    
    subgraph "Dashboard Management"
        UC1[View Real-time Dashboard]
        UC2[Monitor KPI Metrics]
        UC3[View Smart Insights]
        UC4[Check Alerts]
    end
    
    subgraph "Customer Management"
        UC5[Add New Customer]
        UC6[Search Customer]
        UC7[Edit Customer Info]
        UC8[View Customer History]
        UC9[Delete Customer]
    end
    
    subgraph "Transaction Management"
        UC10[Create New Transaction]
        UC11[Update Transaction Status]
        UC12[Calculate Pricing]
        UC13[Schedule Pickup/Delivery]
        UC14[View Transaction List]
        UC15[Edit Transaction]
        UC16[Cancel Transaction]
    end
    
    subgraph "Material Management"
        UC17[Add New Material]
        UC18[Update Stock Level]
        UC19[Monitor Stock Status]
        UC20[Set Reorder Threshold]
        UC21[View Usage Patterns]
        UC22[Edit Material Info]
        UC23[Delete Material]
    end
    
    subgraph "Analytics & Reports"
        UC24[View Business Analytics]
        UC25[Generate Revenue Reports]
        UC26[Analyze Customer Patterns]
        UC27[Monitor Material Usage]
        UC28[Export Data]
    end
    
    subgraph "System Settings"
        UC29[Configure System Settings]
        UC30[Manage Database]
        UC31[View System Status]
        UC32[Backup Data]
    end
    
    %% User connections to use cases
    User --> UC1
    User --> UC2
    User --> UC3
    User --> UC4
    User --> UC5
    User --> UC6
    User --> UC7
    User --> UC8
    User --> UC9
    User --> UC10
    User --> UC11
    User --> UC12
    User --> UC13
    User --> UC14
    User --> UC15
    User --> UC16
    User --> UC17
    User --> UC18
    User --> UC19
    User --> UC20
    User --> UC21
    User --> UC22
    User --> UC23
    User --> UC24
    User --> UC25
    User --> UC26
    User --> UC27
    User --> UC28
    User --> UC29
    User --> UC30
    User --> UC31
    User --> UC32
    
    %% Include relationships
    UC10 -.->|includes| UC6
    UC10 -.->|includes| UC12
    UC10 -.->|includes| UC13
    UC11 -.->|includes| UC4
    UC18 -.->|includes| UC19
    UC5 -.->|includes| UC6
    UC15 -.->|includes| UC12
    UC21 -.->|includes| UC19
    
    %% Extend relationships
    UC3 -.->|extends| UC1
    UC4 -.->|extends| UC1
    UC8 -.->|extends| UC6
    UC21 -.->|extends| UC18
    UC26 -.->|extends| UC24
    UC27 -.->|extends| UC24
    
    %% Styling
    classDef actor fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef dashboard fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef customer fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef transaction fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef material fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef analytics fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef settings fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    
    class User actor
    class UC1,UC2,UC3,UC4 dashboard
    class UC5,UC6,UC7,UC8,UC9 customer
    class UC10,UC11,UC12,UC13,UC14,UC15,UC16 transaction
    class UC17,UC18,UC19,UC20,UC21,UC22,UC23 material
    class UC24,UC25,UC26,UC27,UC28 analytics
    class UC29,UC30,UC31,UC32 settings
```

## 📋 **USE CASE DESCRIPTIONS**

### **Dashboard Management**

#### **UC1: View Real-time Dashboard**
- **Actor**: User
- **Description**: User melihat dashboard utama dengan KPI metrics dan insights
- **Precondition**: User telah mengakses aplikasi
- **Main Flow**: 
  1. User membuka dashboard
  2. System menampilkan real-time metrics
  3. System menampilkan smart insights
  4. User melihat overview bisnis

#### **UC2: Monitor KPI Metrics**
- **Actor**: User
- **Description**: User memantau key performance indicators secara real-time
- **Main Flow**:
  1. User melihat revenue metrics
  2. User melihat transaction counts
  3. User melihat customer metrics
  4. User melihat operational metrics

#### **UC3: View Smart Insights**
- **Actor**: User
- **Description**: User melihat AI-generated business insights
- **Main Flow**:
  1. System menganalisis data patterns
  2. System menghasilkan contextual insights
  3. User melihat actionable recommendations
  4. User dapat expand untuk detail

#### **UC4: Check Alerts**
- **Actor**: User
- **Description**: User memeriksa alerts untuk low stock dan overdue pickups
- **Main Flow**:
  1. System mendeteksi alert conditions
  2. System menampilkan alert notifications
  3. User melihat alert details
  4. User dapat mengambil action

### **Customer Management**

#### **UC5: Add New Customer**
- **Actor**: User
- **Description**: User menambahkan customer baru ke sistem
- **Main Flow**:
  1. User membuka form customer baru
  2. User mengisi informasi customer
  3. System validasi data
  4. System menyimpan customer baru

#### **UC6: Search Customer**
- **Actor**: User
- **Description**: User mencari customer berdasarkan nama atau telepon
- **Main Flow**:
  1. User memasukkan search criteria
  2. System melakukan fuzzy search
  3. System menampilkan hasil pencarian
  4. User memilih customer yang diinginkan

#### **UC7: Edit Customer Info**
- **Actor**: User
- **Description**: User mengubah informasi customer
- **Main Flow**:
  1. User memilih customer untuk diedit
  2. User mengubah informasi
  3. System validasi perubahan
  4. System menyimpan perubahan

### **Transaction Management**

#### **UC10: Create New Transaction**
- **Actor**: User
- **Description**: User membuat transaksi laundry baru
- **Main Flow**:
  1. User membuka form transaksi baru
  2. User memilih customer (includes UC6)
  3. User memilih service type dan weight
  4. System menghitung harga otomatis (includes UC12)
  5. User mengatur jadwal pickup/delivery (includes UC13)
  6. System menyimpan transaksi

#### **UC12: Calculate Pricing**
- **Actor**: User
- **Description**: System menghitung harga berdasarkan service, weight, dan context
- **Main Flow**:
  1. System mengambil base price untuk service
  2. System menghitung berdasarkan weight
  3. System menerapkan weather adjustment
  4. System menerapkan discount jika ada
  5. System menampilkan breakdown harga

#### **UC13: Schedule Pickup/Delivery**
- **Actor**: User
- **Description**: User mengatur jadwal pickup dan delivery
- **Main Flow**:
  1. User memilih tanggal pickup
  2. System mendeteksi day type (weekday/weekend)
  3. System menghitung estimasi delivery
  4. System menampilkan jadwal yang direkomendasikan

### **Material Management**

#### **UC17: Add New Material**
- **Actor**: User
- **Description**: User menambahkan material baru ke inventory
- **Main Flow**:
  1. User membuka form material baru
  2. User mengisi informasi material
  3. User mengatur stock level dan threshold
  4. System menyimpan material baru

#### **UC18: Update Stock Level**
- **Actor**: User
- **Description**: User mengupdate level stock material
- **Main Flow**:
  1. User memilih material
  2. User mengubah stock level
  3. System menghitung stock status (includes UC19)
  4. System menyimpan perubahan

#### **UC19: Monitor Stock Status**
- **Actor**: User
- **Description**: User memantau status stock material
- **Main Flow**:
  1. System menghitung current stock vs threshold
  2. System menentukan status (Safe/Warning/Critical)
  3. System menampilkan visual indicator
  4. System menghasilkan alert jika perlu

### **Analytics & Reports**

#### **UC24: View Business Analytics**
- **Actor**: User
- **Description**: User melihat analytics bisnis komprehensif
- **Main Flow**:
  1. User membuka halaman analytics
  2. System mengambil data analytics
  3. System menampilkan charts dan metrics
  4. User dapat filter berdasarkan periode

#### **UC26: Analyze Customer Patterns**
- **Actor**: User
- **Description**: User menganalisis pola behavior customer
- **Main Flow**:
  1. System menghitung customer patterns
  2. System menampilkan frequency analysis
  3. System menampilkan preference analysis
  4. System memberikan recommendations

## 🔗 **RELATIONSHIP TYPES**

### **Include Relationships**
- **UC10 includes UC6**: Create transaction memerlukan customer search
- **UC10 includes UC12**: Create transaction memerlukan price calculation
- **UC10 includes UC13**: Create transaction memerlukan scheduling

### **Extend Relationships**
- **UC3 extends UC1**: Smart insights memperluas dashboard functionality
- **UC4 extends UC1**: Alerts memperluas dashboard dengan notifications
- **UC8 extends UC6**: Customer history memperluas customer search

## 🎯 **BUSINESS VALUE**

### **Primary Use Cases**
1. **Transaction Management**: Core business operations
2. **Customer Management**: Customer relationship management
3. **Dashboard Monitoring**: Real-time business oversight

### **Supporting Use Cases**
1. **Material Management**: Inventory optimization
2. **Analytics**: Business intelligence
3. **Settings**: System configuration

### **Intelligence Features**
1. **Smart Insights**: AI-powered recommendations
2. **Pattern Analysis**: Predictive analytics
3. **Contextual Pricing**: Dynamic pricing based on conditions
