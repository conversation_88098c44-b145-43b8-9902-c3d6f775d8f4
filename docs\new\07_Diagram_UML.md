# 7. Diagram UML (Unified Modeling Language)

Dokumen ini berisi diagram UML untuk memodelkan aspek-aspek fungsional dari aplikasi `laundrysense`. Diagram yang digunakan adalah Use Case Diagram dan Activity Diagram.

## 7.1. Use Case Diagram

Use Case Diagram ini mengidentifikasi aktor utama dalam sistem dan fungsionalitas utama (kasus penggunaan) yang dapat mereka akses.

**Aktor:**
*   **<PERSON><PERSON><PERSON>**: Pengguna utama aplikasi yang menangani operasi sehari-hari.
*   **Manajer**: Pengguna dengan hak akses lebih tinggi yang fokus pada analisis dan pengawasan.
*   **Sistem (Cron Job)**: Aktor non-manusia yang menjalankan tugas terjadwal.

```mermaid
graph TD
    subgraph "Sistem laundrysense"
        UC1["Kelola Data Pelanggan (CRUD)"]
        UC2["Kelola Data Material (CRUD)"]
        UC3["Kelola Transaksi (CRUD)"]
        UC4["Lihat Dasbor Kontekstual"]
        UC5["Lihat Laporan Analitik"]
        UC6["Hasilkan Pola Perilaku"]
    end

    A1(Karyawan) --> UC1
    A1 --> UC2
    A1 --> UC3
    A1 --> UC4

    A2(Manajer) --|> A1
    A2 --> UC5

    A3(Sistem Cron Job) --> UC6

    UC4 -- "includes" --> UC6
    UC5 -- "includes" --> UC6
```

| Use Case ID | Nama Kasus Penggunaan | Deskripsi Singkat | Aktor Utama |
| :--- | :--- | :--- | :--- |
| **UC1** | Kelola Data Pelanggan | Membuat, melihat, memperbarui, dan menghapus data pelanggan. | Karyawan |
| **UC2** | Kelola Data Material | Membuat, melihat, memperbarui, dan menghapus data material dan stok. | Karyawan |
| **UC3** | Kelola Transaksi | Mencatat transaksi baru, memperbarui statusnya, dan melihat riwayat. | Karyawan |
| **UC4** | Lihat Dasbor Kontekstual | Melihat dasbor utama yang menampilkan wawasan dinamis dan peringatan. | Karyawan, Manajer |
| **UC5** | Lihat Laporan Analitik | Melihat laporan dan visualisasi data historis (tren pendapatan, dll.). | Manajer |
| **UC6** | Hasilkan Pola Perilaku | Menganalisis data historis untuk menghasilkan pola pelanggan dan material. | Sistem (Cron Job) |

---

## 7.2. Activity Diagram

Activity Diagram ini merinci langkah-langkah dalam kasus penggunaan yang lebih kompleks, yaitu "Hasilkan Wawasan Kontekstual". Proses ini terjadi setiap kali pengguna memuat dasbor.

**Aktivitas:** Hasilkan Wawasan Kontekstual
**Aktor:** Sistem (`ContextualEngine`)

```mermaid
graph TD
    A[Start: Permintaan Dasbor Diterima] --> B{Jalankan ContextDetector};
    B --> C[Jalankan TimeAnalyzer];
    B --> D[Jalankan BusinessCycleAnalyzer];
    B --> E[Jalankan DataQualityAnalyzer];

    subgraph "Kumpulkan Konteks"
        C --> F((Gabungkan Hasil));
        D --> F;
        E --> F;
    end

    F --> G{Buat Objek SystemContext};
    G --> H{Jalankan SmartInsightGenerator};
    H --> I{Iterasi Melalui InsightTemplates};
    I --> J{Apakah Konteks Cocok?};
    J -- Ya --> K[Ambil Data Pendukung dari DB];
    K --> L[Buat Objek Insight];
    L --> M((Kumpulkan Wawasan));
    J -- Tidak --> I;

    I -- Selesai Iterasi --> M;
    M --> N[Urutkan Wawasan Berdasarkan Prioritas];
    N --> O[Kirim Wawasan ke Frontend];
    O --> Z[End: Wawasan Ditampilkan];
```

**Deskripsi Langkah-langkah:**
1.  **Permintaan Dasbor Diterima**: Proses dimulai ketika frontend meminta data untuk dasbor.
2.  **Jalankan ContextDetector**: Sistem menginisialisasi proses deteksi konteks.
3.  **Kumpulkan Konteks**: `ContextDetector` menjalankan semua `Analyzer` secara paralel atau berurutan untuk mengumpulkan berbagai aspek konteks (waktu, siklus bisnis, kualitas data).
4.  **Gabungkan Hasil**: Hasil dari semua `Analyzer` digabungkan.
5.  **Buat Objek SystemContext**: Sebuah objek `SystemContext` tunggal dibuat yang merepresentasikan keadaan sistem saat ini.
6.  **Jalankan SmartInsightGenerator**: Generator wawasan diaktifkan, menggunakan `SystemContext` sebagai input.
7.  **Iterasi Melalui InsightTemplates**: Sistem memeriksa setiap template wawasan yang tersedia.
8.  **Apakah Konteks Cocok?**: Untuk setiap template, sistem memeriksa apakah kondisi pemicunya terpenuhi oleh `SystemContext` saat ini.
9.  **Ambil Data Pendukung**: Jika cocok, sistem melakukan query ke database untuk mendapatkan data spesifik yang dibutuhkan oleh template (misalnya, nama pelanggan atau level stok).
10. **Buat Objek Insight**: Data yang diambil digabungkan dengan template untuk membuat objek wawasan yang lengkap.
11. **Kumpulkan Wawasan**: Semua wawasan yang berhasil dibuat dikumpulkan dalam sebuah daftar.
12. **Urutkan Wawasan**: Daftar wawasan diurutkan berdasarkan tingkat kepentingannya (`critical` > `info`).
13. **Kirim Wawasan ke Frontend**: Daftar wawasan yang sudah jadi dikirim sebagai respons API ke frontend.
14. **Wawasan Ditampilkan**: Proses berakhir ketika `ProgressiveInsightCard` di frontend menampilkan wawasan tersebut kepada pengguna.